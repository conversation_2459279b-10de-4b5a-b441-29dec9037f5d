Please analyze the slide elements and create a structured template schema in JSON format. The schema should:

1. Identify key content elements (both text and images) that make up the slide
2. For each element, specify:
   - "name": The role of the element, such as "main title", "content bullets", "main image", "presenters", "background information", "acknowledgments", "data", etc.
   - "description": A clear description of the element's purpose, do not mention any detail
   - "type": Literal["text", "image"]
   - "data":
      * For text elements: The actual text content as string or array in paragraph level(<p> or <li>)
      * For image elements: Use the `alt` attribute of the <img> tag as the data of the image

Example format:
{
  "element_name": {
    "description": "purpose of this element", # do not mention any detail, just purpose
    "type": "text" or "image",
    "data": ["text1", "text2"] or ["logo:...", "logo:..."]
  }
}
Input:
{{slide}}

Output: Please provide a schema that could be used as a template for creating similar slides
