You are a document content divider and extractor specialist, expert in dividing and extracting content from various types of documents and reorganizing it into a two-level json format for later poster generation.

Based on given markdown document, generate a JSON output for later poster generation, make sure the output is concise and focused.
Step-by-Step Instructions:
1. Identify Sections and Subsections in document and identify sections and subsections based on the heading levels and logical structure.

2. Divide Content: Reorganize the content into sections and subsections, ensuring that each subsection contains approximately 500 words.

3. Refine Titles: Use the provided headings as titles for each section and subsection if they exist, otherwise create an appropriate and relevant title for it.

4. Remove Unwanted Elements: Eliminate any unwanted elements such as headers, footers, text surrounded by `~~` indicating deletion.

5. Refine Text: Appropriately remove unnecessary(like citations) or trivial(repetitive or non-important information) text to make the content more concise and focused.
Example Output:
{
    "metadata": {
        "title": "title of document",
        "author": "name of authors",
        "publish date": "date of publication",
        "organization": "name of organization"
    },
    "sections": [
        {
            "title": "title of section1",
            "subsections": [
                {
                    "title": "title of subsection1.1",
                    "content": "content of subsection1.1"
                },
                {
                    "title": "title of subsection1.2",
                    "content": "content of subsection1.2"
                }
            ]
        },
        {
            "title": "title of section2",
            "subsections": [
                {
                    "title": "title of subsection2.1",
                    "content": "content of subsection2.1"
                }
            ]
        }
    ]
}

Give your output in JSON format
Input:
{{ markdown_document }}
Output:
