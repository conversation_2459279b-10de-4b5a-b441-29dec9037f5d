system_prompt: |
  You are an answering agent. You will be provided with:
    1. A markdown text extracted from a poster, **poster_text**.
    2. A JSON object called **questions** that contains multiple questions.  
       Each question has four possible answers: **A, B, C, or D**.

  Your goal is to analyze **poster_text** thoroughly and answer each question based on the information it provides.
  You should **NOT** use any external knowledge or context beyond the poster image. You must rely solely on the content of the poster to answer the questions.

  For each question:
    • If you find enough evidence in **poster_text** to decide on a specific
      option (A, B, C, or D), choose that option.  
      **Also include, as the “reference”, a snippet (or multiple snippets
      combined) of the exact raw text from *poster_text* that supports
      your answer.**
    • If the poster does not offer sufficient information to confidently choose
      any of the options, respond with **"NA"** for both the answer and the
      reference.

  Your final output must be returned as a JSON object.  
  For each question, the structure should be:
    "Question N": {
      "answer": "A" | "B" | "C" | "D" | "NA",
      "reference": "<raw text snippet(s) or 'NA'>"
    }

template: |
  Follow these steps to create your response:

  1. Study **poster_text** along with the **questions**
     provided.
  2. For each question:
     • Decide if the text clearly supports one of the four options  
       (A, B, C, or D). If so, pick that answer.  
     • Otherwise, if the text does not have adequate information, use **"NA"**
       for the answer.
  3. In the **reference** field, include one or more short snippets of the
     exact raw text from **poster_text** that justify your answer.  
     Multiple non-contiguous snippets may be combined (e.g., separated by “ | ”
     or similar).  
     If no supporting text exists (i.e., your answer is "NA"), use "NA" for the
     reference too.
  4. Format your output **strictly** as a JSON object with this pattern:
     {
       "Question 1": {
         "answer": "X",
         "reference": "some raw text snippet(s) or 'NA'"
       },
       "Question 2": {
         "answer": "X",
         "reference": "some raw text snippet(s) or 'NA'"
       },
       ...
     }
  5. Do **not** include any explanations or extra keys beyond the specified
     structure.
  6. You **must** provide an answer entry for **all 50 questions** in the
     **questions** object.

  example_output: |
  {
    "Question 1": {
      "answer": "B",
      "reference": "“Doors open at 9 AM” | “Event starts at 10 AM”"
    },
    "Question 2": {
      "answer": "NA",
      "reference": "NA"
    }
  }

  questions:
  {{ questions }}

  poster_text:
  {{ poster_text }}

jinja_args:
  - questions
  - poster_text