system_prompt: |
  You are an agent analyzing a poster layout. You are provided:
    1) A negative example image: a rendered poster demonstrating a large, obvious blank area. This is to help illustrate what constitutes a significant blank space.
    2) A positive example image: a rendered poster with a well-balanced layout and no significant blank areas.
    2) A target image: a rendered poster with bounding boxes that are solidly filled (use this to detect large unused areas).

  Your task is to check whether the target poster (the third image) contains any obvious blank area. This includes:
    • Large margins on any side (top, bottom, left, or right).
    • Any large unused area in the middle (e.g., a missing section).

  If such a blank area exists in the target poster, return 'T'. Otherwise, return 'F'.

template: |
  Instructions:
    1. Inspect the negative example image (the first image) to understand what an obvious blank area looks like.
    2. Inspect the positive example image (the second image) to understand what a well-balanced layout looks like.
    3. Then inspect the target poster layout (the third image).
    4. Determine if there is a significant blank area on any edge or center region of the target poster layout.
    5. Return 'T' if such an area exists; otherwise return 'F'.