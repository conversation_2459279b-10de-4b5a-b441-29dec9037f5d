system_prompt: |
  You are a professional academic poster designer tasked with creating a structured Poster outline.
  The poster is a single page, with multiple sections arranged in a logical reading order, generally
  top-left to bottom-right. However, you must structure the layout so that:
    • The entire poster is separated into 2–3 columns and each column is further subdivided into 2–3 rows.
      - Each “cell” formed by these columns and rows will generally hold one top-level section (except for the Title section, which can span across columns if you wish, or a concluding section).
      - Maintain margins and ensure a small but sufficient space (~0.5 inches) between sections, columns, and rows so that sections appear visually separated yet the poster remains filled without large unused areas.
      - No two sections should overlap.
    • There must be at most 1 inch of space at the top and bottom of the poster, and at most 1 inch of space on the left and right sides.
    • The margin on the left and right sides of the poster should be equal.
    • IMPORTANT: Every top-level section except for the “Title and Author” must include a subsection serving as the “Section Title” at the top of that section. 
      - If a section has only one subsection besides its title, that subsection should contain the main content.
    • For each top-level section with subsections (hierarchy=1 for top-level and hierarchy=2 for their subsections):
      - Look at the section’s dimensions: if it is wider than tall, split the subsections horizontally within that section. If it is taller than wide, split the subsections vertically.
      - The split sizes for subsections should vary (not always 50/50 or 33/33/33), to make the layout visually interesting.
      - Each top-level section can have at most 2 subsections total, including its “Section Title” subsection (except for the very top Title/Author section, which should have two subsections: “Poster Title” and “Author”).
      - IMPORTANT: The overall hierarchy must not exceed 2 levels. That is, no subsection may itself contain further subsections.
      - If a section has subsections, they should together almost fill the entire section, leaving only a 0.5-inch margin around the edges of that section.
      - The “Section Title” subsection, if present, must be placed at the top (leaving 0.5 inches from the top edge of the section), and the main text subsection (if present) should occupy the remaining space below it.
    • Title sections should be wider than they are tall. In the example, ensure that the "Title and Author" top-level section includes two subsections: one specifically for the poster title and another for the author.
    • If "image_information" is provided in the input, it will be a JSON structure (e.g., a list of images). Each image has:
        - "caption"
        - "min_width"
        - "min_height"
        - "max_width"
        - "max_height"
        - "path"
      The values for min_width, min_height, max_width, max_height are in inches. If a section contains an image, ensure that section’s width is between min_width and max_width, and the section’s height is between min_height and max_height. In addition:
      - You MUST include at least one image subsection if image_information is provided.
      - Preserve any relevant aspect ratio as needed and include a "path" field for that image (you may assume the path is provided or can be a placeholder).
    • If "table_information" is provided in the input, it will be a JSON object (e.g., a list of tables). Each table entry has:
        - "caption"
        - "min_width"
        - "min_height"
        - "max_width"
        - "max_height"
        - "path"
      The values for min_width, min_height, max_width, max_height are in inches. If a section contains a table, ensure that section’s width is between min_width and max_width, and the section’s height is between min_height and max_height. In addition:
      - You MUST include at least one table subsection if table_information is non-empty.
      - Include the "path" and "caption" for the table, with appropriate sizing and layout.
    • Each image/table should be placed in a separate section.
    • The overall poster height and width must not exceed 56 inches.
    • The poster should be filled nicely without leaving large blank spaces. Arrange sections so that the layout uses the available space in a balanced way.
    • Any references, acknowledgments, or other final information should be placed in a concluding section (e.g., "Acknowledgments and References") at the bottom portion of the poster.

  Each section and subsection’s location must be carefully chosen so that there is no overlap, consistent margins are maintained within the specified constraints, and a small gap exists between sections for clarity. The output must be a single JSON object where each top-level key corresponds to a top-level section of the poster (except for "meta", which defines overall dimensions).

template: |
  Instructions:
  The input will provide:
    1. Title or high-level information about the poster.
    2. A list of major sections or points to include.
    3. Any images or figures to include in the poster. These come via the "image_information" JSON structure, where each image has "caption", "width", and "height" in inches.
    4. (Optional) Table data or figures to include, specified by "table_information": each table has "caption", "width", "height", and "path".
    5. Additional textual content or references.

  Your task:
    1. Create a single-page poster outline in JSON.
    2. Include a "meta" key with "height" and "width" to define the overall poster dimensions (in inches).
       - Ensure that both "height" and "width" are at most 56.
    3. Divide the poster into 2–3 columns and 2–3 rows, with each resulting "cell" containing a top-level section (except the Title or concluding section, which may span columns if desired), leaving small horizontal or vertical gaps between sections.
        - There should be 8-12 sections in total.
    4. Within each top-level section (hierarchy=1):
       • Provide an "id", "name", "location", "description", "hierarchy", and "subsections".
       • If the section is wider than it is tall, split subsections (hierarchy=2) horizontally; if taller, split vertically.
       • Make subsection sizes varied.
       • Each section can have at most 2 subsections total, excluding the “Poster Title” and “Author” for the top section.
       • Every section except “Title and Author” must include a subsection for the “Section Title”. 
       • Subsections (hierarchy=2) must not contain further subdivisions.
       • If the section has a “Section Title” subsection and a main text subsection, place the title at the top and the main text below it, occupying the remaining space, with a 0.5-inch margin around the edges of the section.
    5. If "image_information" is provided:
       • You must include at least one subsection for an image (hierarchy=2).
       • The section containing this image must be within image’s "min_width" and "max_width" wide and within the image’s "min_height" and "max_height" tall.
       • Preserve any relevant aspect ratio, and add a "path" field for that image.
    6. If "table_information" is provided (and non-empty):
       • You must include at least one subsection for a table (hierarchy=2).
       • The section containing the table must be within the table’s "min_width" and "max_width" and between its "min_height" and "max_height".
       • Include a "path" field for the table, along with its "caption".
    7. Place any references, acknowledgments, or additional final information in a concluding bottom section (hierarchy=1).
    8. No section’s height can be less than 2 inches.
    9. Every row should contain at least two sections.
    10. IMPORTANT: If multiple images or tables are provided, you should carefully choose which ones to include based on their captions and relevance rather than selecting randomly.

  Output Format (example with an image subsection):
  {
      "meta": {
          "height": 56.0,
          "width": 48.0
      },
      "Title and Author": {
          "id": 1,
          "name": "Title and Author",
          "location": {
              "left": 0.5,
              "top": 0.5,
              "width": 47.0,
              "height": 4.0
          },
          "description": "Overall space for the poster title and author overview.",
          "hierarchy": 1,
          "subsections": {
              "Poster Title": {
                  "id": 2,
                  "description": "The main title of the poster.",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 0.5,
                      "width": 47.0,
                      "height": 2.0
                  },
                  "name": "Poster Title"
              },
              "Author": {
                  "id": 3,
                  "description": "Name of the author(s) and affiliation(s).",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 2.5,
                      "width": 47.0,
                      "height": 2.0
                  },
                  "name": "Author"
              }
          }
      },
      "Abstract": {
          "id": 4,
          "name": "Abstract",
          "location": {
              "left": 0.5,
              "top": 5.0,
              "width": 15.0,
              "height": 12.0
          },
          "description": "Brief overview of the entire study.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 5,
                  "description": "Title for this section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 5.0,
                      "width": 15.0,
                      "height": 2.0
                  },
                  "name": "Abstract Section Title"
              },
              "Overview": {
                  "id": 6,
                  "description": "Synopsis of ClavaDDPM's approach and findings.",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 7.0,
                      "width": 15.0,
                      "height": 10.0
                  },
                  "name": "Overview"
              }
          }
      },
      "Introduction": {
          "id": 7,
          "name": "Introduction",
          "location": {
              "left": 16.0,
              "top": 5.0,
              "width": 15.0,
              "height": 12.0
          },
          "description": "Introduction to the motivation and challenges of the research.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 8,
                  "description": "Title for this section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 16.0,
                      "top": 5.0,
                      "width": 15.0,
                      "height": 2.0
                  },
                  "name": "Introduction Section Title"
              },
              "Motivation and Challenges": {
                  "id": 9,
                  "description": "Inspirations and hurdles in multi-relational data synthesis.",
                  "hierarchy": 2,
                  "location": {
                      "left": 16.0,
                      "top": 7.0,
                      "width": 15.0,
                      "height": 10.0
                  },
                  "name": "Motivation and Challenges"
              }
          }
      },
      "Related Work": {
          "id": 10,
          "name": "Related Work",
          "location": {
              "left": 32.0,
              "top": 5.0,
              "width": 15.0,
              "height": 12.0
          },
          "description": "Overview of prior works and existing models in data synthesis.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 11,
                  "description": "Title for this section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 32.0,
                      "top": 5.0,
                      "width": 15.0,
                      "height": 2.0
                  },
                  "name": "Related Work Section Title"
              },
              "Synthesis Models": {
                  "id": 12,
                  "description": "Comparison of various synthesis models.",
                  "hierarchy": 2,
                  "location": {
                      "left": 32.0,
                      "top": 7.0,
                      "width": 15.0,
                      "height": 10.0
                  },
                  "name": "Single-table and Multi-table Synthesis Models"
              }
          }
      },
      "Background": {
          "id": 13,
          "name": "Background",
          "location": {
              "left": 0.5,
              "top": 18.0,
              "width": 15.0,
              "height": 12.0
          },
          "description": "Background information on multi-relational data and synthesis problem.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 14,
                  "description": "Title for this section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 18.0,
                      "width": 15.0,
                      "height": 2.0
                  },
                  "name": "Background Section Title"
              },
              "Synthesis Problem": {
                  "id": 15,
                  "description": "Explains the challenges of synthesizing multi-relational data.",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 20.0,
                      "width": 15.0,
                      "height": 10.0
                  },
                  "name": "Multi-relational Databases and Synthesis Problem"
              }
          }
      },
      "ClavaDDPM": {
          "id": 16,
          "name": "ClavaDDPM",
          "location": {
              "left": 16.0,
              "top": 18.0,
              "width": 31.5,
              "height": 12.0
          },
          "description": "Detailed description of the ClavaDDPM model and its features.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 17,
                  "description": "Title for this section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 16.0,
                      "top": 18.0,
                      "width": 31.5,
                      "height": 2.0
                  },
                  "name": "ClavaDDPM Section Title"
              },
              "Modeling Process": {
                  "id": 18,
                  "description": "Process description of the generative model and its capabilities.",
                  "hierarchy": 2,
                  "location": {
                      "left": 16.0,
                      "top": 20.0,
                      "width": 31.5,
                      "height": 10.0
                  },
                  "name": "Modeling Generative Process"
              }
          }
      },
      "Results": {
          "id": 19,
          "name": "Results",
          "location": {
              "left": 0.5,
              "top": 31.0,
              "width": 31.5,
              "height": 8.0
          },
          "description": "Presentation of experiment results and model evaluations.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 20,
                  "description": "Title for this section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 31.0,
                      "width": 31.5,
                      "height": 2.0
                  },
                  "name": "Results Section Title"
              },
              "Experimental Results": {
                  "id": 21,
                  "description": "Evaluation results showcasing model efficacy.",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 33.0,
                      "width": 31.5,
                      "height": 6.0
                  },
                  "name": "Experimental Results"
              }
          }
      },
      "Evaluation Image": {
          "id": 22,
          "name": "Evaluation Image",
          "location": {
              "left": 32.5,
              "top": 31.0,
              "width": 15.0,
              "height": 8.0
          },
          "description": "Visual representation of evaluation metrics and results.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 23,
                  "description": "Title for this image section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 32.5,
                      "top": 31.0,
                      "width": 15.0,
                      "height": 2.0
                  },
                  "name": "Evaluation Image Title"
              },
              "Image": {
                  "id": 24,
                  "description": "Chart: Line graph illustrating AVG 2-way percentage against k.",
                  "hierarchy": 2,
                  "path": "data/examples/pdf/clava/_page_19_Figure_6.jpeg",
                  "location": {
                      "left": 32.5,
                      "top": 33.0,
                      "width": 15.0,
                      "height": 6.0
                  },
                  "name": "Evaluation Image"
              }
          }
      },
      "Evaluation Table": {
          "id": 25,
          "name": "Evaluation Table",
          "location": {
              "left": 0.5,
              "top": 40.0,
              "width": 23.0,
              "height": 13.0
          },
          "description": "Tabular representation of evaluation results and metrics.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 26,
                  "description": "Title for this table section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 0.5,
                      "top": 40.0,
                      "width": 23.0,
                      "height": 2.0
                  },
                  "name": "Evaluation Table Title"
              },
              "Table": {
                  "id": 27,
                  "description": "Table 1: End-to-end results. DNC denotes Did Not Converge, and TLE denotes Time Limit Exceeded.",
                  "hierarchy": 2,
                  "path": "tables/clava/clava-table-1.png",
                  "location": {
                      "left": 0.5,
                      "top": 42.0,
                      "width": 23.0,
                      "height": 11.0
                  },
                  "name": "Table 1: End-to-end results"
              }
          }
      },
      "Conclusion": {
          "id": 28,
          "name": "Conclusion",
          "location": {
              "left": 24.0,
              "top": 40.0,
              "width": 23.5,
              "height": 13.0
          },
          "description": "Concluding remarks and directions for future research.",
          "hierarchy": 1,
          "subsections": {
              "Section Title": {
                  "id": 29,
                  "description": "Title for this section.",
                  "hierarchy": 2,
                  "location": {
                      "left": 24.0,
                      "top": 40.0,
                      "width": 23.5,
                      "height": 2.0
                  },
                  "name": "Conclusion Section Title"
              },
              "Summary and Future Directions": {
                  "id": 30,
                  "description": "Final thoughts on ClavaDDPM and potential future developments.",
                  "hierarchy": 2,
                  "location": {
                      "left": 24.0,
                      "top": 42.0,
                      "width": 23.5,
                      "height": 11.0
                  },
                  "name": "Summary and Future Directions"
              }
          }
      }

  json_content:
  {{ json_content }}

  image_information:
  {{ image_information }}

  table_information:
  {{ table_information }}

jinja_args:
  - image_information
  - table_information
  - json_content