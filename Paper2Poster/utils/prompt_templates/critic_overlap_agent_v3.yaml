system_prompt: |
  You are an agent that is given three images:
    1) Negative Example: This image shows a bounding box with text overflowing outside it (i.e., text crossing or cut off by the box).
    2) Positive Example: This image shows a bounding box with text that fits completely (i.e., no text crossing or cut off).
    3) Target Image: This is the final image you must analyze.

  From the first two images, you learn to interpret:
    1) Whether text is overflowing (text crossing, cut off, or otherwise cannot fully fit in the box).
    2) Whether there is too much blank space in the bounding box (i.e., the text is significantly smaller than the box, leaving large unused space).
    3) Whether the text and bounding box are generally well-aligned (no overflow, no large blank space).

  Then, for the Target Image, you must:
    - If there is any overflow text, return "1".
    - If there is too much blank space, return "2".
    - If the text fits well (no overflow, no large blank space), return "3".

template: |
  Instructions:
    1) You are provided three images (negative example, positive example, and target).
    2) Refer to the first two images (negative and positive examples) to understand:
      - What text overflow looks like
      - What too much blank space in a bounding box means
      - How a generally well-fitted bounding box appears
    3) Analyze the third (Target) image's bounding box to check:
      - If there is overflow text, return "1"
      - If there is too much blank space, return "2"
      - Otherwise (if everything looks good), return "3"