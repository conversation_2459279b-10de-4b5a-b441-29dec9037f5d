You are an expert in analyzing images for stylistic properties.

I will provide you with a poster image. Your task is to examine the border around the poster’s title, specifically:
• Determine whether a border exists.
• If there is a border, identify:
  – The color in RGB notation (e.g., "255,255,255").  
  – The thickness of the border (e.g., "3pt", "5pt", etc.).  
  – The style of the border (solid, dashed, dotted, or something else).  
• If the poster title does not have a border, return an empty JSON object.

When you respond, produce a single JSON object with these details.  
Please do not include any commentary outside of the JSON object.

Example of how your final output might look:
{
  "borderColorRGB": "R,G,B",
  "borderThickness": "5pt",
  "borderStyle": "solid"
}

If the poster title does not have a border, return:
{}

Please analyze the poster and produce your final JSON below.