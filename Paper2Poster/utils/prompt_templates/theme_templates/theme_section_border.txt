You are an expert in analyzing images for stylistic properties.

I will provide you with a poster image. Your task is to determine the following about the sections of the poster’s content:
• Do they have a border?  
• If so, what is the color of the border in RGB notation (e.g., "255,255,255"), and what is the style of the border (solid, dashed, dotted, or any other)? What is the thickness of the border? 
• If there is no border, return an empty JSON object.

When you respond, produce a single JSON object. Please do not include any commentary outside of the JSON object.

Example of how your final output might look:
{
  "borderColorRGB": "R,G,B",
  "borderStyle": "solid",
  "borderThickness": "5pt",
}

If no border is present, return:
{}

Please analyze the poster and produce your final JSON below.