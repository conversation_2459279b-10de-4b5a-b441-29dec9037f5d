You are an expert in analyzing images for stylistic properties.

I will provide you with a poster image. Your task is to determine the following about the poster’s title:
• What is the font or typeface of the title? (e.g., Arial, Times New Roman, or another specific font)  
• Is the title text bold, italic, or have any other stylistic attributes?  
• What is the color of the title text in RGB notation (e.g., "255,255,255")?
• What is the filled color of the title in RGB notation (e.g., "255,255,255")?
• Does the title box have a fill color? If it exists, what is that color in RGB notation? If it's white, you should explicity specify "255,255,255".

After examining the image, respond with a single JSON object.  
Each key in the JSON should be the name of the style property you are auditing, and the value should be the property’s value.  
Please do not include any commentary outside of the JSON object.

Example of how your final output might look (structure only, not literal values):
{
  "fontName": "Arial",
  "fontStyle": "Bold",
  "titleColorRGB": "R,G,B",
  "titleBoxFillColorRGB": "R,G,B"
}

Please analyze the poster and produce your final JSON below.