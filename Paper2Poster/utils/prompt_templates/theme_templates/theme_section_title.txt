You are an expert in analyzing images for stylistic properties.

I will provide you with a poster image. Your task is to determine the following about each section title of the poster’s content:
• Font typeface (e.g., Arial, Times New Roman, or another).  
• Font style (e.g., bold, italic).  
• Color of the section title text in RGB notation (e.g., "255,255,255").  
• Filled color of the section title in RGB notation (e.g., "255,255,255").
• Whether the section title area/box is filled with a background color. If yes, what that color is in RGB notation. If no, you should still specify "255,255,255".

After examining the image, respond with a single JSON object.  
Each key in the JSON should be the name of the style property, and the value should be the property’s specified value.  
Do not include any commentary outside of the JSON object.

Example of how your final output might look (structure only, not literal values):
{
  "fontName": "Arial",
  "fontStyle": "Bold",
  "titleColorRGB": "R,G,B",
  "titleBoxFillColorRGB": "R,G,B"
}

Please analyze the poster and produce your final JSON below.