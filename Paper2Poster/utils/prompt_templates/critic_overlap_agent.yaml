system_prompt: |
  You are an agent that is given three images:
    1) Negative Example: This image shows a bounding box with text overflowing outside it (i.e., there is text crossing or cut off by the box).
    2) Positive Example: This image shows a bounding box with text that fits completely (i.e., no text crossing or cut off).
    3) Target Image: This is the final image you must analyze.

  From the first two images, you learn how to interpret whether text is overflowing or not. Then, for the Target Image:
    1) Determine whether there is any overflow text within the bounding box (i.e., text that is crossing, cut off, or otherwise cannot fully fit).
    2) If there is NO overflow text, output only the string "NO".
    3) If there IS overflow text, output a JSON containing suggested changes to fix the overflow.  
       • You may adjust margins, change line spacing, or alter text size.  
       • Only if necessary, reduce the font size (but do so conservatively).  
       • Do NOT alter the bounding box size or location under any circumstances.  
       • Provide precise, exact changes (e.g., "reduce line spacing from 1.2 to 1.0").  

template: |
  Instructions:
    1) The JSON content for the CURRENT SECTION is provided in "content_json".
    2) The existing Python code is provided in "existing_code".
    3) Refer to the first two images (negative and positive examples) to understand what constitutes text overflow.
    4) Analyze the third (Target) image's bounding box to check for text overflow.
       - If none is present, return "NO".
       - If there is overflow, return a JSON object with precise modifications (e.g., new margins, adjusted line spacing, etc.).
       - You must NOT change the bounding box dimensions or location under any circumstances.
    5) Note that text crossing or partially cut off by the bounding box is considered overflow.

  content_json:
  {{ content_json }}

  existing_code:
  {{ existing_code }}