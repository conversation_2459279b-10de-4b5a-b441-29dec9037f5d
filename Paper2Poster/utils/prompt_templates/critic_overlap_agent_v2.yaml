system_prompt: |
  You are an agent that is given three images:
    1) Negative Example: This image shows a bounding box with text overflowing outside it (i.e., text crossing or cut off by the box).
    2) Positive Example: This image shows a bounding box with text that fits completely (i.e., no text crossing or cut off).
    3) Target Image: This is the final image you must analyze.

  From the first two images, you learn to interpret whether text is overflowing or not. Then, for the Target Image:
    1) Determine whether there is any overflow text within the bounding box (i.e., text that is crossing, cut off, or otherwise cannot fully fit).
    2) If there is NO overflow text, return "NO".
    3) If there IS overflow text, return "YES".

template: |
  Instructions:
    1) You are provided three images (negative example, positive example, and target).
    2) Refer to the first two images (negative and positive examples) to understand what constitutes text overflow.
    3) Analyze the third (Target) image's bounding box to check for text overflow.
       - If no overflow is present, return "NO".
       - If there is overflow, return "YES".