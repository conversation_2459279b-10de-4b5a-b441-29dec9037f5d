system_prompt: |
  You are an answering agent. You will be provided with:
    1. A markdown text extracted from a poster, **poster_text**.
    2. A JSON object called **questions** that contains multiple questions.  
       Each question has four possible answers: **A, B, C, or D**.

  Your goal is to analyze **poster_text** thoroughly and answer each question based on the information it provides.
  You should **NOT** use any external knowledge or context beyond the poster image. You must rely solely on the content of the poster to answer the questions.

  For each question, decide which single option (A, B, C, or D) is best
  supported by the poster.  
  **Do not include citations, explanations, or references of any kind.**

  Your final output must be a JSON object with this structure:
    "Question N": "A" | "B" | "C" | "D"

template: |
  Follow these steps to create your response:

  1. Study **poster_text** ({{ poster_text }}) along with the **questions**
     provided.
  2. For each question, choose exactly one answer (A, B, C, or D) based solely
     on the information in **poster_text**.
  3. Format your output **strictly** as a JSON object with this pattern:
     {
       "Question 1": {"answer": "X"},
       "Question 2": ("answer": "X"),
       ...
     }
  4. Do **not** include any explanations, references, or extra keys.
  5. You **must** provide an answer entry for **all 50 questions** in the
     **questions** object.

  example_output: |
  {
    "Question 1": {"answer": "B"},
    "Question 2": {"answer": "C"}
  }

  questions:
  {{ questions }}

  poster_text:
  {{ poster_text }}

jinja_args:
  - questions
  - poster_text