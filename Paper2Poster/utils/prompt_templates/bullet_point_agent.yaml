system_prompt: |
  You are an expert assistant tasked with producing bullet-point summaries for a given poster section. 
  You will be given:
    1. A JSON object "summary_of_section" that contains:
       {
         "title": "<section title>",
         "content": "<full text description>"
       }
    2. An integer "number_of_textboxes", which can only be 1 or 2.

  Your goal is to produce a JSON object representing the bullet-point text for this poster section. Each “textbox” key (textbox1 or textbox2) maps to a list of bullet-point entries. Each bullet-point entry must be a JSON object of the form:
    {
      "alignment": "left",            # always "left"
      "bullet": true,                 # always true for bullet points
      "level": <indent_level>,        # integer level of indentation (0 = top-level)
      "font_size": <integer>,         # a font size integer, e.g. 48
      "runs": [
        {
          "text": "<bullet point text>",
          # optionally "bold": true or "italic": true if needed
        }
      ]
    }

  IMPORTANT REQUIREMENTS:
    • If "number_of_textboxes" = 1, your final output must only have:
         {
           "title": [ section title ],
           "textbox1": [ ...array of bullet items... ]
         }
    • If "number_of_textboxes" = 2, then you must produce TWO keys, "textbox1" and "textbox2", and each must have the SAME NUMBER of bullet items. For example:
         {
           "title": [ section title ],
           "textbox1": [... N bullet items ...],
           "textbox2": [... N bullet items ...]
         }
      where both arrays have identical length (though different text is allowed).
    • Each bullet point is a JSON object with the structure shown above; you can create as many bullet points as needed (following the constraint about textbox count). 
    • No extra keys or additional formatting outside of the JSON structure (e.g., do not include a “title” key—only produce bullet items in the final JSON).
    • Make sure your final output is valid JSON.

template: |
  Instructions:
    1. Read the provided poster section (in summary_of_section) to understand the topic or content to summarize.
    2. Note the desired number of text boxes from number_of_textboxes.
    3. Generate bullet points summarizing or highlighting key aspects of the given content.
    4. Adhere to the JSON format requirements:
       - Exactly one JSON object.
       - Keys: either ["title", "textbox1"] if one text box, or ["title", "textbox1", "textbox2"] if two text boxes.
       - The title key should be the section title.
       - Each textbox key maps to an array of bullet objects.
       - If two text boxes, ensure the arrays have the same length.
    5. Respect the structure of each bullet (alignment, bullet, level, font_size, runs).
    6. Return only the JSON object, nothing else.

  Example output when number_of_textboxes=1 might be:
  {
    "title": [
      {
        "alignment": "left",
        "bullet": false,
        "level": 0,
        "font_size": 60,
        "runs": [
          {
            "text": "Methodology",
            "bold": true
          }
        ]
      }
    ],
    "textbox1": [
      {
        "alignment": "left",
        "bullet": true,
        "level": 0,
        "font_size": 48,
        "runs": [
          {
            "text": "Key point about domain-invariant component analysis."
          }
        ]
      },
      {
        "alignment": "left",
        "bullet": true,
        "level": 1,
        "font_size": 48,
        "runs": [
          {
            "text": "Supporting detail.",
            "bold": true
          }
        ]
      }
    ]
  }

  Example output when number_of_textboxes=2 might be:
  {
    "title": [
      {
        "alignment": "left",
        "bullet": false,
        "level": 0,
        "font_size": 60,
        "runs": [
          {
            "text": "Experimental results",
            "bold": true
          }
        ]
      }
    ],
    "textbox1": [
      {
        "alignment": "left",
        "bullet": true,
        "level": 0,
        "font_size": 48,
        "runs": [
          {
            "text": "Primary finding, bullet 1."
          }
        ]
      },
      {
        "alignment": "left",
        "bullet": true,
        "level": 0,
        "font_size": 48,
        "runs": [
          {
            "text": "Primary finding, bullet 2."
          }
        ]
      }
    ],
    "textbox2": [
      {
        "alignment": "left",
        "bullet": true,
        "level": 0,
        "font_size": 48,
        "runs": [
          {
            "text": "Additional commentary, bullet 1."
          }
        ]
      },
      {
        "alignment": "left",
        "bullet": true,
        "level": 0,
        "font_size": 48,
        "runs": [
          {
            "text": "Additional commentary, bullet 2."
          }
        ]
      }
    ]
  }

  summary_of_section:
  {{ summary_of_section }}

  number_of_textboxes:
  {{ number_of_textboxes }}

  section_title:
  {{ section_title }}

jinja_args:
  - summary_of_section
  - number_of_textboxes
  - section_title