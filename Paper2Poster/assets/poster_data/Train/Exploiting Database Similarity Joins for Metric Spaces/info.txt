<Poster Width="1734" Height="1419">
	<Panel left="25" right="203" width="534" height="594">
		<Text>The Problem</Text>
		<Text>• Similarity joins are a key tool in analyzing and</Text>
		<Text>processing data.</Text>
		<Text>• Some standalone Similarity Join algorithms</Text>
		<Text>have been proposed.</Text>
		<Text>• Little work on implementing Similarity Joins as</Text>
		<Text>physical database operators has been done.</Text>
		<Text>Our Contribution</Text>
		<Text>• DBSimJoin, a general Similarity Join database</Text>
		<Text>operator for metric spaces implemented inside</Text>
		<Text>PostgreSQL.</Text>
		<Text>• Non-blocking behavior</Text>
		<Text>• Prioritizes early generation of results</Text>
		<Text>• Fully supports the iterator interface</Text>
		<Text>• We show how this operator can be used in</Text>
		<Text>real-world data analysis scenarios:</Text>
		<Text>• Identify similar images (vectors)</Text>
		<Text>• Identify similar publications (strings)</Text>
	</Panel>

	<Panel left="18" right="832" width="566" height="555">
		<Text>DBSimJoin Algorithm</Text>
		<Text>• Partitions data in</Text>
		<Text>successive rounds</Text>
		<Text>until the partitions</Text>
		<Text>are small enough to</Text>
		<Text>be joined with a</Text>
		<Text>nested loop.</Text>
		<Text>• Partitioning is done</Text>
		<Text>in a series of</Text>
		<Text>rounds.</Text>
		<Text>• The algorithm is</Text>
		<Text>structured as a</Text>
		<Text>finite-state machine</Text>
		<Text>in order to support</Text>
		<Text>the database</Text>
		<Text>iterator interface.</Text>
		<Figure left="281" right="922" width="271" height="389" no="1" OriWidth="0.352941" OriHeight="0.358289
" />
	</Panel>

	<Panel left="574" right="202" width="1123" height="593">
		<Text>Partitioning in DBSimJoin</Text>
		<Text>• The data is partitioned in</Text>
		<Text>a generalized hyperplane</Text>
		<Text>using a set of K pivots.</Text>
		<Text>• Two types of partitions</Text>
		<Text>exist: base partitions and</Text>
		<Text>window-pair partitions.</Text>
		<Text>• Each data record is</Text>
		<Text>placed into the base</Text>
		<Text>partition of its closest</Text>
		<Text>pivot.</Text>
		<Text>• Window partitions hold</Text>
		<Text>data that is within ε of</Text>
		<Text>the boundary between</Text>
		<Text>partitions.</Text>
		<Figure left="922" right="306" width="333" height="330" no="2" OriWidth="0.348904" OriHeight="0.210339
" />
		<Text> Partitioning a Base Partition</Text>
		<Figure left="1319" right="358" width="363" height="269" no="3" OriWidth="0.32699" OriHeight="0.174242
" />
		<Text> Partitioning a Window Partition</Text>
	</Panel>

	<Panel left="603" right="835" width="655" height="543">
		<Text>DBSimJoin Rounds</Text>
		<Text>• The first round</Text>
		<Text>partitions the</Text>
		<Text>input data. All</Text>
		<Text>partitions too large</Text>
		<Text>to be processed</Text>
		<Text>immediately in-</Text>
		<Text>memory are stored</Text>
		<Text>on-disk.</Text>
		<Text>• Additional rounds</Text>
		<Text>re-partition</Text>
		<Text>partitions that</Text>
		<Text>have been stored</Text>
		<Text>on disk.</Text>
		<Figure left="866" right="946" width="366" height="337" no="4" OriWidth="0.350058" OriHeight="0.174242
" />
	</Panel>

	<Panel left="1281" right="833" width="417" height="548">
		<Text>Performance</Text>
		<Text> Increasing Scale Factor</Text>
		<Figure left="1336" right="922" width="332" height="186" no="5" OriWidth="0" OriHeight="0
" />
		<Text> Increasing Epslion</Text>
	</Panel>

</Poster>