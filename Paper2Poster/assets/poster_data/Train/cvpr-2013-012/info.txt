<Poster Width="1735" Height="1227">
	<Panel left="19" right="160" width="842" height="264">
		<Text>Objective</Text>
		<Text>Input: Single low-resolution, noisy, and perhaps heavily quantized depth map</Text>
		<Text>Objective: Jointly increase spatial resolution and apparent measurement accuracy (e.g., depth resolution)</Text>
		<Figure left="40" right="248" width="804" height="146" no="1" OriWidth="0.332948" OriHeight="0.1715818
" />
		<Text> Left: 3x nearest neighbor upscaling. Right: 3x SR output of our algorithm.</Text>
	</Panel>

	<Panel left="18" right="431" width="420" height="198">
		<Text>Contributions</Text>
		<Text>‘Single image’ depth SR—using information only from</Text>
		<Text>input depth map—by:</Text>
		<Text>• Reasoning in terms of 3D point patches</Text>
		<Text>• 3D variant of PatchMatch</Text>
		<Text>• Patch upscaling and merging technique</Text>
	</Panel>

	<Panel left="18" right="636" width="419" height="524">
		<Text>Why is it Hard?</Text>
		<Text>Most techniques rely on ancillary data that is often</Text>
		<Text>unavailable or difficult to obtain (e.g., aligned guiding</Text>
		<Text>image at target resolution).</Text>
		<Text>Proceeding ‘by example’—by assembling SR out-</Text>
		<Text>put from matched 2D pixel patches—poses its own</Text>
		<Text>challenges:</Text>
		<Text>• Different patch depths (depth normalization?)</Text>
		<Text>• Projective distortions (calls for small patches)</Text>
		<Text>• Object boundaries (discontinuity handling?)</Text>
		<Figure left="59" right="943" width="337" height="171" no="2" OriWidth="0.345087" OriHeight="0.131367
" />
		<Text> Left: three dissimilar pairs of 2D pixel patches.</Text>
		<Text> Right: analogous 3D point patch pairs similar.</Text>
	</Panel>

	<Panel left="445" right="432" width="416" height="345">
		<Text>3D Point Patches</Text>
		<Figure left="460" right="476" width="396" height="203" no="3" OriWidth="0.349711" OriHeight="0.141197
" />
		<Text>6 DoF 3D rigid body motion g ∈ SE(3) relating 3D point</Text>
		<Text>patches Sx , Sx0 ⊂ R3 . Point Px is point encoded at pixel x of</Text>
		<Text>input depth map and is center point of ‘further’ patch Sx .</Text>
		<Text>Point P0</Text>
		<Text>x = g(Px ) is center point of ‘closer’ patch Sx0 .</Text>
		<Text>Radius r is kept same for all patches.</Text>
	</Panel>

	<Panel left="445" right="783" width="416" height="369">
		<Text>Matching Cost c(x; g)</Text>
		<Text>b‘Backward’ cost c (x; g) computes patch similarity by</Text>
		<Text>SSD over nearest neighbors of S</Text>
		<Text>x in g −1 (S</Text>
		<Text>x0 ), which</Text>
		<Text>does not penalize addition of new detail. To be more</Text>
		<Text>confident that such new detail is reasonable, we also</Text>
		<Text>compute analogous ‘forward’ cost cf (x; g).</Text>
		<Figure left="468" right="956" width="190" height="91" no="4" OriWidth="0" OriHeight="0
" />
		<Text> cb (x;g).‘Backward’ cost</Text>
		<Figure left="670" right="952" width="186" height="96" no="5" OriWidth="0" OriHeight="0
" />
		<Text> ‘Forward’ costcf (x;g).</Text>
		<Text>Matching cost c(x; g) over which we minimize given by</Text>
		<Text>convex combination of ‘backward’ and ‘forward’ cost.</Text>
	</Panel>

	<Panel left="868" right="160" width="418" height="262">
		<Text>Algorithm</Text>
		<Text>Our proposed ‘single image’ depth SR algorithm re-</Text>
		<Text>duces to two steps:</Text>
		<Text>1. Obtain dense 6 DoF correspondence field over in-</Text>
		<Text>put pixels x using new 3D variant of PatchMatch</Text>
		<Text>algorithm of Barnes et al.</Text>
		<Text>ˆ of output depth map at2. Populate SR pixels x</Text>
		<Text>target resolution using novel patch upscaling and</Text>
		<Text>merging technique</Text>
	</Panel>

	<Panel left="1296" right="162" width="411" height="263">
		<Text>Dense 6 DoF Correspondence Search</Text>
		<Figure left="1323" right="212" width="176" height="146" no="6" OriWidth="0.153757" OriHeight="0.0991957
" />
		<Figure left="1514" right="210" width="178" height="148" no="7" OriWidth="0.154335" OriHeight="0.0978552
" />
		<Text>Visualization of projected 3D displacements of output dense</Text>
		<Text>6 DoF rigid body assignment of our 3D PatchMatch variant.</Text>
	</Panel>

	<Panel left="868" right="430" width="841" height="319">
		<Text>Patch Upscaling and Merging</Text>
		<Text>SR output generated by weighted sum over interpolated depth values of ‘backward’-transformed points g</Text>
		<Text>x−1 (S</Text>
		<Text>x0 </Text>
		<Text>Patch weight computed as function of cb (x; gx ) in order to promote addition of new detail.</Text>
		<Figure left="886" right="542" width="268" height="136" no="8" OriWidth="0.243353" OriHeight="0.0875782
" />
		<Figure left="1205" right="521" width="234" height="157" no="9" OriWidth="0.195954" OriHeight="0.100983
" />
		<Figure left="1468" right="518" width="230" height="153" no="10" OriWidth="0.198844" OriHeight="0.102324
" />
		<Text>−1At input resolution, ‘backward’-transformed 3D points gx(Sx0 ) allowed to influence only 2D pixels corresponding to Sx , since it is</Text>
		<Text>over these pixels that matching cost c(x; gx ) was computed. At target resolution, we carry out polygon approximation of pixel</Text>
		<Text>−1ˆ of polygonalized mask that depth values from gxmask. It is over SR pixels x(Sx0 ) are interpolated.</Text>
	</Panel>

	<Panel left="867" right="755" width="844" height="250">
		<Text>Qualitative Results</Text>
		<Figure left="896" right="801" width="253" height="174" no="11" OriWidth="0.506358" OriHeight="0.285076
" />
		<Text> 2x NN and SR (stereo).</Text>
		<Figure left="1189" right="798" width="213" height="175" no="12" OriWidth="0.295376" OriHeight="0.1894548
" />
		<Text> 2x NN and SR (structured light).</Text>
		<Figure left="1437" right="800" width="269" height="177" no="13" OriWidth="0.369942" OriHeight="0.1949866
" />
		<Text> 4x NN and SR (ToF).</Text>
	</Panel>

	<Panel left="869" right="1014" width="838" height="177">
		<Text>Quantitative Results (Middlebury)</Text>
		<Figure left="892" right="1061" width="391" height="112" no="14" OriWidth="0.571098" OriHeight="0.145219
" />
		<Text> Root mean square error (RMSE).</Text>
		<Figure left="1298" right="1058" width="399" height="117" no="15" OriWidth="0.550867" OriHeight="0.142538
" />
		<Text> Percent error.</Text>
	</Panel>

</Poster>