# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
from .anthropic_model import AnthropicModel
from .azure_openai_model import AzureOpenAIModel
from .base_model import BaseModelBackend
from .cohere_model import CohereModel
from .deepseek_model import DeepSeekModel
from .fish_audio_model import FishAudioModel
from .gemini_model import GeminiModel, DeepInfraGeminiModel
from .groq_model import GroqModel
from .internlm_model import InternLMModel
from .litellm_model import LiteLLMModel
from .mistral_model import MistralModel
from .model_factory import ModelFactory
from .model_manager import ModelManager, ModelProcessingError
from .nemotron_model import NemotronModel
from .nvidia_model import NvidiaModel
from .ollama_model import OllamaModel
from .openai_audio_models import OpenAIAudioModels
from .openai_compatible_model import OpenAICompatibleModel
from .openai_model import OpenAIModel
from .qwen_model import QwenModel, DeepInfraPhi4Model
from .reka_model import RekaModel
from .samba_model import SambaModel
from .sglang_model import SGLangModel
from .stub_model import StubModel
from .togetherai_model import TogetherAIModel
from .vllm_model import VLLMModel
from .yi_model import YiModel
from .zhipuai_model import ZhipuAIModel
from .openrouter_model import OpenRouterModel

__all__ = [
    'BaseModelBackend',
    'OpenAIModel',
    'AzureOpenAIModel',
    'AnthropicModel',
    'MistralModel',
    'GroqModel',
    'StubModel',
    'ZhipuAIModel',
    'CohereModel',
    'ModelFactory',
    'ModelManager',
    'LiteLLMModel',
    'OpenAIAudioModels',
    'NemotronModel',
    'NvidiaModel',
    'OllamaModel',
    'VLLMModel',
    'SGLangModel',
    'GeminiModel',
    'OpenAICompatibleModel',
    'OpenAICompatibleModelV2',
    'RekaModel',
    'SambaModel',
    'TogetherAIModel',
    'YiModel',
    'QwenModel',
    'ModelProcessingError',
    'DeepSeekModel',
    'FishAudioModel',
    'InternLMModel',
    'OpenRouterModel',
    'DeepInfraPhi4Model',
    'DeepInfraGeminiModel',
]
