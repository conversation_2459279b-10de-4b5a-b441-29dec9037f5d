accelerate
agentops==0.3.26
aiofiles==24.1.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiohttp-client-cache==0.11.1
aiohttp-cors==0.7.0
aiosignal==1.3.2
aiosqlite==0.20.0
airportsdata==20241001
alabaster==1.0.0
annotated-types==0.7.0
anthropic==0.42.0
antlr4-python3-runtime==4.9.3
anyio==4.8.0
apify_client==1.8.1
apify_shared==1.2.1
appdirs==1.4.4
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
arxiv==2.1.3
arxiv2text==0.1.14
asgiref==3.8.1
asknews==0.7.58
astor==0.8.1
asttokens==3.0.0
async-lru==2.0.4
async-timeout==4.0.3
asyncer==0.0.8
asyncio==3.4.3
attrs==24.3.0
av==14.1.0
azure-core==1.32.0
azure-storage-blob==12.24.1
babel==2.16.0
backoff==2.2.1
backports.tarfile==1.2.0
beautifulsoup4==4.12.3
bibtexparser==1.4.3
black==25.1.0
blake3==1.0.2
bleach==6.2.0
botocore==1.36.9
Brotli==1.1.0
build==1.2.2.post1
CacheControl==0.14.2
cachetools==5.5.0
cairocffi==1.7.1
CairoSVG==2.7.1
cattrs==24.1.2
certifi==2024.12.14
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.1
cleo==2.1.0
click==8.1.8
cloudpickle==3.1.1
cohere==5.13.11
colorama==0.4.6
coloredlogs==15.0.1
colorful==0.5.6
comm==0.2.2
compressed-tensors
contourpy==1.3.1
crashtest==0.4.1
cryptography==42.0.6
cssselect==1.2.0
cssselect2==0.8.0
curl_cffi==0.6.2
cycler==0.12.1
dappier==0.3.3
dataclasses-json==0.6.7
datacommons==1.4.3
datacommons-pandas==0.0.3
datasets==3.2.0
debugpy==1.8.12
decorator==5.1.1
deepdiff==8.1.1
deepsearch-glm==1.0.0
defusedxml==0.8.0rc2
Deprecated==1.2.18
depyf==0.18.0
diffusers==0.25.1
dill==0.3.8
discord.py==2.4.0
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
docker==7.1.0
docstring-parser==0.15
docutils==0.21.2
docx2txt==0.8
duckdb==1.1.3
duckduckgo_search==6.4.2
dulwich==0.21.7
e2b==1.0.6
e2b-code-interpreter==1.0.4
easyocr
effdet
einops==0.8.0
emoji==2.14.1
et_xmlfile==2.0.0
eval_type_backport==0.2.0
evaluate==0.4.3
exceptiongroup==1.2.2
executing==2.1.0
fake-useragent==2.0.3
Faker==19.13.0
fast-depends==2.4.12
fastapi==0.115.6
fastavro==1.10.0
fastjsonschema==2.21.1
feedfinder2==0.0.4
feedparser==6.0.11
ffmpeg-python==0.2.0
ffmpy==0.5.0
filelock==3.16.1
filetype==1.2.0
firecrawl-py==1.6.8
fish-audio-sdk==2024.12.5
FlagEmbedding
flatbuffers==25.1.24
fonttools==4.55.8
fqdn==1.5.1
free_proxy==1.1.3
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2024.9.0
ftfy==6.3.1
func-argparse==1.1.1
future==1.0.0
geojson==2.5.0
gguf==0.10.0
google-ai-generativelanguage==0.6.4
google-api-core==2.24.0
google-api-python-client==2.160.0
google-auth==2.37.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.1
google-cloud-storage==2.19.0
google-cloud-vision==3.9.0
google-crc32c==1.6.0
google-generativeai==0.6.0
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
googlemaps==4.10.0
gradio_client==1.7.0
greenlet==3.1.1
grpcio==1.67.1
grpcio-status==1.62.3
grpcio-tools==1.62.3
h11==0.14.0
h2==4.1.0
hpack==4.1.0
html5lib==1.1
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.4
httpx==0.27.2
httpx-sse==0.4.0
httpx-ws==0.7.1
huggingface-hub==0.27.1
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
imageio==2.37.0
imagesize==1.4.1
importlib_metadata==8.4.0
iniconfig==2.0.0
installer==0.7.0
interegular==0.3.3
iopath==0.1.10
ipykernel==6.29.5
ipython==8.31.0
ipywidgets==8.1.5
isodate==0.7.2
isoduration==20.11.0
itsdangerous==2.2.0
jaraco.classes==3.4.0
jaraco.context==6.0.1
jedi==0.19.2
jeepney==0.8.0
jieba3k==0.35.1
Jinja2==3.1.5
jiter==0.8.2
jmespath==1.0.1
joblib==1.4.2
json5==0.10.0
json_repair==0.35.0
jsonlines==3.1.0
jsonpatch==1.33
jsonpath-python==1.0.6
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-path==0.3.4
jsonschema-specifications==2024.10.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.11.0
jupyter-kernel-gateway==3.0.1
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
jupyterlab==4.3.4
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
kaleido==0.2.1
keyring==24.3.1
kiwisolver==1.4.8
langchain==0.3.17
langchain-community==0.3.16
langchain-core==0.3.33
langchain-openai==0.3.3
langchain-text-splitters==0.3.5
langdetect==1.0.9
langsmith==0.3.3
lark==1.2.2
latex2mathml==3.77.0
layoutparser==0.3.4
lazy-object-proxy==1.10.0
lazy_loader==0.4
Levenshtein==0.26.1
linkify-it-py==2.0.3
linkup-sdk==0.2.2
litellm==1.59.10
lm-format-enforcer==0.10.9
lxml==5.3.0
Markdown==3.7
markdown-it-py==3.0.0
markdownify==0.13.1
marker-pdf
marko==2.1.2
MarkupSafe==2.1.5
marshmallow==3.26.0
matplotlib==3.10.0
matplotlib-inline==0.1.7
mdit-py-plugins==0.4.2
mdurl==0.1.2
memray==1.15.0
milvus-lite==2.4.11
mistral_common==1.5.1
mistralai==1.5.0
mistune==3.1.0
monotonic==1.6
more-itertools==10.6.0
mpire==2.10.2
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.1.0
multiprocess==0.70.16
multitasking==0.0.11
mypy-extensions==1.0.0
narwhals==1.38.2
nbclient==0.10.2
nbconvert==7.16.5
nbformat==5.10.4
nebula3-python==3.8.2
neo4j==5.27.0
nest-asyncio==1.6.0
networkx==3.4.2
newspaper3k==0.2.8
ninja==********
nltk==3.9.1
notebook==7.3.2
notebook_shim==0.2.4
notion-client==2.3.0
numpy==1.26.4
oaib==1.2.0
oauthlib==3.2.2
olefile==0.47
omegaconf==2.3.0
onnx==1.17.0
onnxruntime==1.20.1
openai==1.59.8
openapi-schema-validator==0.6.3
openapi-spec-validator==0.7.1
openbb==4.3.5
openbb-benzinga==1.3.5
openbb-bls==1.0.3
openbb-cftc==1.0.3
openbb-commodity==1.2.6
openbb-core==1.3.8
openbb-crypto==1.3.5
openbb-currency==1.3.5
openbb-derivatives==1.3.5
openbb-econdb==1.2.5
openbb-economy==1.3.5
openbb-equity==1.3.5
openbb-etf==1.3.5
openbb-federal-reserve==1.3.5
openbb-fixedincome==1.3.5
openbb-fmp==1.3.5
openbb-fred==1.3.5
openbb-imf==1.0.2
openbb-index==1.3.5
openbb-intrinio==1.3.5
openbb-news==1.3.5
openbb-oecd==1.3.5
openbb-platform-api==1.0.4
openbb-polygon==1.3.5
openbb-regulators==1.3.5
openbb-sec==1.3.5
openbb-tiingo==1.3.5
openbb-tradingeconomics==1.3.5
openbb-us-eia==1.0.0
openbb-yfinance==1.3.6
opencensus==0.11.4
opencensus-context==0.1.3
opencv-python==4.11.0.86
opencv-python-headless==4.11.0.86
openpyxl==3.1.5
opentelemetry-api==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-http==1.27.0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
orderly-set==5.2.3
orjson==3.10.15
ormsgpack==1.7.0
outcome==1.3.0.post0
outlines
outlines_core==0.1.26
overrides==7.7.0
packaging==24.2
pandas
pandasai
pandoc==2.4
pandocfilters==1.5.1
parso==0.8.4
partial-json-parser==0.2.1.1.post5
pathable==0.4.4
pathlib==1.0.1
pathspec==0.12.1
pdf2image==1.17.0
pdfminer.six==20231228
pdfplumber==0.11.5
pdftext==0.4.1
peewee==3.17.8
peft==0.14.0
pexpect==4.9.0
pi_heif==0.21.0
pikepdf==9.5.1
pillow==10.4.0
pkginfo==1.12.0
platformdirs==4.3.6
playwright==1.51.0
plotly==6.0.1
pluggy==1.5.0
plumbum==1.9.0
ply==3.11
poetry==1.8.5
poetry-core==1.9.1
poetry-plugin-export==1.8.0
portalocker==2.10.1
posthog==3.11.0
prance==*********
praw==7.8.1
prawcore==2.4.0
primp==0.11.0
prometheus-fastapi-instrumentator==7.0.2
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.2.1
proto-plus==1.25.0
protobuf==4.25.6
psutil==6.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
py-spy==0.4.0
pyarrow==19.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pyautogen==0.7.3
pybind11==2.13.6
pyclipper==1.3.0.post6
pycocotools==2.0.8
pycountry==24.6.1
pycparser==2.22
pydantic==2.9.2
pydantic-settings==2.7.1
pydantic_core==2.23.4
pydub==0.25.1
pydyf==0.11.0
pyee==12.1.1
PyGithub==2.5.0
Pygments==2.19.1
PyJWT==2.10.1
pymilvus==2.5.4
PyMuPDF==1.25.2
PyNaCl==1.5.0
pyowm==3.3.0
pypandoc==1.15
pyparsing==3.2.1
pypdf==5.2.0
PyPDF2==3.0.1
pypdfium2==4.30.0
pyphen==0.17.2
pyproject_hooks==1.2.0
pysbd==0.3.4
PySocks==1.7.1
pyTelegramBotAPI==4.26.0
pytesseract==0.3.13
pytest==8.3.4
python-bidi==0.6.3
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-iso639==2025.1.28
python-json-logger==3.2.1
python-Levenshtein==0.26.1
python-magic==0.4.27
python-multipart==0.0.18
python-oxmsg==0.0.1
python-pptx 
pytorch-fid==0.3.0
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
qdrant-client==1.13.2
ragas==0.1.6
rank-bm25==0.2.2
RapidFuzz==3.11.0
ray==2.40.0
redis==5.2.1
referencing==0.36.1
regex==2024.11.6
reka-api==3.2.0
requests==2.32.3
requests-cache==1.2.1
requests-file==2.1.0
requests-oauthlib==1.3.1
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.9.4
rouge==1.0.1
rpds-py==0.22.3
rsa==4.9
Rtree==1.3.0
ruamel.yaml==0.18.10
ruamel.yaml.clib==0.2.12
ruff==0.7.4
safehttpx==0.1.6
safetensors==0.5.2
scholarly==1.7.11
scikit-image==0.25.1
scikit-learn==1.6.1
scipy==1.15.1
SecretStorage==3.3.3
selenium==4.28.1
semantic-version==2.10.0
semchunk==2.2.2
Send2Trash==1.8.3
sentence-transformers==3.3.1
sentencepiece==0.2.0
setproctitle==1.3.4
sglang==0.4.2
sgmllib3k==1.0.0
shapely==2.0.7
shellingham==1.5.4
six==1.17.0
slack_bolt==1.22.0
slack_sdk==3.34.0
smart-open==7.1.0
sniffio==1.3.1
snowballstemmer==2.2.0
socksio==1.0.0
sortedcontainers==2.4.0
soundfile==0.13.1
soupsieve==2.6
Sphinx==8.1.3
sphinx-rtd-theme==3.0.2
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
SQLAlchemy==2.0.37
sqlglot==25.34.1
sqlglotrs==0.3.0
stack-data==0.6.3
starlette==0.41.3
stem==1.8.2
stripe==11.5.0
surya-ocr
sympy==1.13.1
tabled-pdf==0.2.0
tabulate==0.9.0
tavily-python==0.5.0
tenacity==9.0.0
termcolor==2.4.0
terminado==0.18.1
texify
textblob==0.17.1
textual==1.0.0
threadpoolctl==3.5.0
tifffile==2025.1.10
tiktoken==0.7.0
timm==1.0.13
tinycss2==1.4.0
tinyhtml5==2.0.0
tinysegmenter==0.3
tldextract==5.1.3
tokenizers==0.21.0
tomli==2.2.1
tomlkit==0.13.2
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.48.0
tree-sitter==0.23.2
tree-sitter-python==0.23.6
trio==0.28.0
docling_core>=2.0.0
docling_parse
docling_ibm_models
trio-websocket==0.11.1
triton
trove-classifiers==2025.1.15.22
typer==0.12.5
types-python-dateutil==2.9.0.20241206
types-requests==2.32.0.20241016
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
uc-micro-py==1.0.3
ujson==5.10.0
unstructured==0.16.11
unstructured-client==0.28.1
unstructured-inference==0.8.1
unstructured.pytesseract==0.3.13
update-checker==0.18.0
uri-template==1.3.0
uritemplate==4.1.1
url-normalize==1.4.3
urllib3==2.3.0
uuid7==0.1.0
uvicorn==0.32.1
uvloop==0.21.0
virtualenv==20.29.1
vllm==0.6.6.post1
watchfiles==1.0.4
wcwidth==0.2.13
WeasyPrint==52.5
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==14.1
widgetsnbextension==4.0.13
wikipedia==1.4.0
wolframalpha==5.1.3
wordcloud==1.9.4
wrapt==1.17.2
wsproto==1.2.0
xformers==0.0.28.post3
xgrammar==0.1.10
xlrd==2.0.1
XlsxWriter==3.2.0
xmltodict==0.13.0
xxhash==3.5.0
yarl==1.18.3
yfinance==0.2.52
yt-dlp==2024.12.23
zipp==3.21.0
zopfli==0.2.3.post1
zstandard==0.23.0
