# 配置文件（包含API密钥）
config.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/

# 上传的文件
uploads/*.pdf

# 数据文件
data/papers.json

# 向量数据库文件
vector_store/

# 预阅读文件
pre_reads/*.md
hidden_pre_reads/*.md

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log 