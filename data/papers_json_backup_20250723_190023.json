[{"title": "484098-基于自适应权重选择机 散模型研究-附加材料-c38539ab", "authors": "tt 谭", "abstract": "No abstract available", "date": "2025-07-23", "source": "PDF Upload", "has_pdf": true, "id": 1, "file_path": "uploads/20250723_174211_484098-基于自适应权重选择机_散模型研究-附加材料-c38539ab.pdf"}, {"title": "Reinforcement Learning with Action Chunking", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "abstract": "We present Q-chunking, a simple yet effective recipe for improving\nreinforcement learning (RL) algorithms for long-horizon, sparse-reward tasks.\nOur recipe is designed for the offline-to-online RL setting, where the goal is\nto leverage an offline prior dataset to maximize the sample-efficiency of\nonline learning. Effective exploration and sample-efficient learning remain\ncentral challenges in this setting, as it is not obvious how the offline data\nshould be utilized to acquire a good exploratory policy. Our key insight is\nthat action chunking, a technique popularized in imitation learning where\nsequences of future actions are predicted rather than a single action at each\ntimestep, can be applied to temporal difference (TD)-based RL methods to\nmitigate the exploration challenge. Q-chunking adopts action chunking by\ndirectly running RL in a 'chunked' action space, enabling the agent to (1)\nleverage temporally consistent behaviors from offline data for more effective\nonline exploration and (2) use unbiased $n$-step backups for more stable and\nefficient TD learning. Our experimental results demonstrate that Q-chunking\nexhibits strong offline performance and online sample efficiency, outperforming\nprior best offline-to-online methods on a range of long-horizon, sparse-reward\nmanipulation tasks.", "date": "2025-07-10", "source": "arXiv:2507.07969", "arxiv_id": "2507.07969", "file_path": "uploads/arxiv_2507.07969_20250723_174331.pdf", "has_pdf": true, "id": 2}]