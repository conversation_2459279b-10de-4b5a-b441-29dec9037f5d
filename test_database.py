#!/usr/bin/env python3
"""
Test script for database functionality
"""

from database import DatabaseManager

def test_database():
    """Test all database functionality"""
    print("开始测试数据库功能...")
    
    # 测试数据库基本功能
    db = DatabaseManager()
    print("✓ 数据库连接成功")

    # 测试论文操作
    papers = db.get_all_papers()
    print(f"✓ 获取论文列表成功，共 {len(papers)} 篇论文")

    # 测试用户操作
    users = db.get_user_by_username("default")
    print(f"✓ 获取默认用户成功，用户ID: {users['id']}")

    # 测试会话创建
    if papers:
        session_id = db.create_chat_session(papers[0]["id"], users["id"])
        print(f"✓ 创建聊天会话成功，会话ID: {session_id}")
        
        # 测试消息添加
        msg_id = db.add_chat_message(session_id, "user", "测试消息")
        print(f"✓ 添加聊天消息成功，消息ID: {msg_id}")
        
        # 测试消息获取
        messages = db.get_chat_messages(session_id)
        print(f"✓ 获取聊天消息成功，共 {len(messages)} 条消息")
        
        # 测试会话获取
        sessions = db.get_paper_chat_sessions(papers[0]["id"])
        print(f"✓ 获取论文会话成功，共 {len(sessions)} 个会话")

    # 测试统计信息
    stats = db.get_database_stats()
    print(f"✓ 数据库统计: {stats}")

    print("\n所有数据库功能测试通过！")
    return True

if __name__ == "__main__":
    test_database()
