<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DocuMancer - 论文精灵</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 左侧功能栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="app-logo">
                    <h1 class="app-name">DocuMancer</h1>
                    <p class="app-name-chinese">论文精灵</p>
                </div>
            </div>
            <div class="sidebar-content">
                <!-- 功能栏内容，后续扩展 -->
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="library-container">
                <!-- 头部区域 -->
                <div class="library-header">
                    <div class="header-content">
                        <h1 class="library-title">
                            <span class="title-icon">📚</span>
                            我的论文库
                        </h1>
                        <p class="library-subtitle">上传PDF或添加arXiv论文，开始智能阅读之旅</p>
                    </div>
                    
                    <!-- 添加论文区域 -->
                    <div class="add-paper-section">
                        <div class="upload-options">
                            <!-- PDF上传 -->
                            <div class="upload-option">
                                <input type="file" id="pdfUpload" accept=".pdf" style="display: none;">
                                <button class="upload-btn" onclick="document.getElementById('pdfUpload').click()">
                                    <span class="btn-icon">📄</span>
                                    <span class="btn-text">上传PDF</span>
                                </button>
                            </div>
                            
                            <!-- arXiv ID输入 -->
                            <div class="upload-option">
                                <div class="arxiv-input-container">
                                    <input type="text" id="arxivInput" placeholder="输入arXiv ID (例如: 2301.07041)" class="arxiv-input">
                                    <button id="addArxivBtn" class="add-arxiv-btn">
                                        <span class="btn-icon">🔗</span>
                                        添加
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 论文卡片网格 -->
                <div class="papers-grid" id="papersGrid">
                    {% for paper in papers %}
                    <div class="paper-card" data-paper-id="{{ paper.id }}">
                        <div class="card-header">
                            <div class="paper-source">{{ paper.source }}</div>
                            <div class="card-header-right">
                                <div class="top-row">
                                    {% if paper.has_pdf %}
                                    <span class="pdf-badge pdf-available">📄 PDF</span>
                                    {% else %}
                                    <span class="pdf-badge pdf-unavailable">❌ 无PDF</span>
                                    {% endif %}
                                    <div class="card-menu">
                                        <button class="menu-trigger" onclick="toggleMenu({{ paper.id }})">
                                            <span class="dots">⋯</span>
                                        </button>
                                        <div class="menu-dropdown" id="menu-{{ paper.id }}">
                                            <button class="menu-item delete-button" data-paper-id="{{ paper.id }}" data-paper-title="{{ paper.title|e }}">
                                                <span class="menu-icon">🗑️</span>
                                                删除论文
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="paper-title">{{ paper.title }}</h3>
                            <p class="paper-authors">{{ paper.authors }}</p>
                            <p class="paper-abstract">{{ paper.abstract }}</p>
                            <div class="paper-date-bottom">{{ paper.date }}</div>
                        </div>
                        <div class="card-footer">
                            <div class="card-actions">
                                <button class="chat-button" data-paper-id="{{ paper.id }}" onclick="goToChat({{ paper.id }})">
                                    <span class="chat-icon">💬</span>
                                    开始对话
                                </button>
                                {% if paper.has_pdf %}
                                <button class="pre-read-button" data-paper-id="{{ paper.id }}" onclick="goToPreRead({{ paper.id }})">
                                    <span class="pre-read-icon">📖</span>
                                    预阅读
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- 空状态 -->
                {% if not papers %}
                <div class="empty-state">
                    <div class="empty-icon">📖</div>
                    <h3 class="empty-title">还没有论文</h3>
                    <p class="empty-message">上传您的第一篇PDF论文或添加arXiv论文来开始使用</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text">正在处理...</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html> 