<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预阅读 - {{ paper.title }} - DocuMancer</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- 引入marked.js用于Markdown渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 左侧功能栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="app-logo">
                    <h1 class="app-name">DocuMancer</h1>
                    <p class="app-name-chinese">论文精灵</p>
                </div>
            </div>
            <div class="sidebar-content">
                <!-- 返回论文库按钮 -->
                <button class="back-button" onclick="goBack()">
                    <span class="back-icon">←</span>
                    返回论文库
                </button>
                
                <!-- 切换到聊天按钮 -->
                <button class="back-button chat-switch-btn" data-paper-id="{{ paper.id }}">
                    <span class="back-icon">💬</span>
                    切换到聊天
                </button>
                
                <!-- 论文信息 -->
                <div class="paper-info">
                    <h3 class="info-title">当前论文</h3>
                    <div class="paper-details">
                        <p class="paper-title-sidebar">{{ paper.title }}</p>
                        <p class="paper-authors-sidebar">{{ paper.authors }}</p>
                        <p class="paper-source-sidebar">{{ paper.source }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="pre-read-page-container">
                <div class="chat-header">
                    <h2 class="chat-title">📖 论文预阅读</h2>
                    <p class="chat-subtitle">{{ paper.title }}</p>
                </div>
                
                {% if pre_read_exists %}
                <!-- 预阅读内容 -->
                <div class="pre-read-content" id="preReadContent" data-content="{{ pre_read_content|tojson|forceescape }}">
                    <!-- Markdown内容将通过JavaScript渲染 -->
                </div>
                {% else %}
                <!-- 空状态 -->
                <div class="empty-pre-read">
                    <div class="empty-icon-large">📚</div>
                    <h3>还没有预阅读内容</h3>
                    <p>点击下方按钮生成AI预阅读分析</p>
                    <button class="generate-pre-read-btn" data-paper-id="{{ paper.id }}">
                        <span id="generateBtnText">🚀 生成预阅读</span>
                    </button>
                    <div id="loadingSpinner" class="loading-spinner hidden"></div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        const paperId = {{ paper.id }};
        const preReadExists = {{ pre_read_exists|tojson }};
        
        document.addEventListener('DOMContentLoaded', function() {
            // 如果有预阅读内容，渲染Markdown
            if (preReadExists) {
                const preReadContentElement = document.getElementById('preReadContent');
                if (preReadContentElement) {
                    const preReadContent = JSON.parse(preReadContentElement.getAttribute('data-content'));
                    preReadContentElement.innerHTML = marked.parse(preReadContent);
                }
            }
            
            // 绑定切换到聊天按钮
            const chatBtn = document.querySelector('.chat-switch-btn');
            if (chatBtn) {
                chatBtn.addEventListener('click', function() {
                    const paperId = this.getAttribute('data-paper-id');
                    window.location.href = '/chat/' + paperId;
                });
            }
            
            // 绑定生成预阅读按钮
            const generateBtn = document.querySelector('.generate-pre-read-btn');
            if (generateBtn) {
                generateBtn.addEventListener('click', function() {
                    const paperId = this.getAttribute('data-paper-id');
                    generatePreRead(paperId);
                });
            }
        });
        
        function goBack() {
            window.location.href = '/';
        }
        
        function generatePreRead(paperId) {
            const btn = document.querySelector('.generate-pre-read-btn');
            const spinner = document.getElementById('loadingSpinner');
            const btnText = document.getElementById('generateBtnText');
            
            // 显示加载状态
            btn.disabled = true;
            btnText.textContent = '正在生成预阅读...';
            spinner.classList.remove('hidden');
            
            fetch('/generate_pre_read/' + paperId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 刷新页面显示生成的预阅读
                    window.location.reload();
                } else {
                    alert('预阅读生成失败: ' + (data.error || '未知错误'));
                    // 恢复按钮状态
                    btn.disabled = false;
                    btnText.textContent = '🚀 生成预阅读';
                    spinner.classList.add('hidden');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('预阅读生成失败，请稍后重试');
                // 恢复按钮状态
                btn.disabled = false;
                btnText.textContent = '🚀 生成预阅读';
                spinner.classList.add('hidden');
            });
        }
    </script>
</body>
</html> 