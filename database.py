import sqlite3
import json
import os
from datetime import datetime
from typing import List, Dict, Optional, Any
from contextlib import contextmanager


class DatabaseManager:
    """
    Database manager class for DocuMancer application
    Handles SQLite database operations for papers, users, and chat history
    """
    
    def __init__(self, db_path: str = "data/documancer.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """Ensure data directory exists"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """Initialize database tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Create papers table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS papers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    authors TEXT,
                    abstract TEXT,
                    date TEXT,
                    source TEXT,
                    file_path TEXT,
                    has_pdf BOOLEAN DEFAULT 0,
                    arxiv_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            """)
            
            # Create chat sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS chat_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    paper_id INTEGER NOT NULL,
                    user_id INTEGER,
                    session_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (paper_id) REFERENCES papers (id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
                )
            """)
            
            # Create chat messages table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS chat_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER NOT NULL,
                    role TEXT NOT NULL,  -- 'user' or 'assistant'
                    content TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT,  -- JSON string for additional data
                    FOREIGN KEY (session_id) REFERENCES chat_sessions (id) ON DELETE CASCADE
                )
            """)
            
            # Create indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_papers_arxiv_id ON papers (arxiv_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_papers_title ON papers (title)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_chat_sessions_paper_id ON chat_sessions (paper_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages (session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_chat_messages_timestamp ON chat_messages (timestamp)")
            
            conn.commit()
    
    # Paper operations
    def add_paper(self, paper_data: Dict[str, Any]) -> int:
        """Add a new paper to database"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO papers (title, authors, abstract, date, source, file_path, has_pdf, arxiv_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                paper_data.get('title', ''),
                paper_data.get('authors', ''),
                paper_data.get('abstract', ''),
                paper_data.get('date', ''),
                paper_data.get('source', ''),
                paper_data.get('file_path', ''),
                paper_data.get('has_pdf', False),
                paper_data.get('arxiv_id', '')
            ))
            conn.commit()
            return cursor.lastrowid
    
    def get_paper(self, paper_id: int) -> Optional[Dict[str, Any]]:
        """Get a paper by ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM papers WHERE id = ?", (paper_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_all_papers(self) -> List[Dict[str, Any]]:
        """Get all papers"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM papers ORDER BY created_at DESC")
            return [dict(row) for row in cursor.fetchall()]
    
    def update_paper(self, paper_id: int, update_data: Dict[str, Any]) -> bool:
        """Update a paper"""
        if not update_data:
            return False
        
        set_clause = ", ".join([f"{key} = ?" for key in update_data.keys()])
        values = list(update_data.values()) + [paper_id]
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(f"""
                UPDATE papers 
                SET {set_clause}, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            """, values)
            conn.commit()
            return cursor.rowcount > 0
    
    def delete_paper(self, paper_id: int) -> bool:
        """Delete a paper"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM papers WHERE id = ?", (paper_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def check_duplicate_paper(self, title: str, authors: str = None) -> Optional[Dict[str, Any]]:
        """Check if paper already exists based on title and authors"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if authors:
                cursor.execute("""
                    SELECT * FROM papers 
                    WHERE title = ? AND authors = ?
                    LIMIT 1
                """, (title, authors))
            else:
                cursor.execute("""
                    SELECT * FROM papers 
                    WHERE title = ?
                    LIMIT 1
                """, (title,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    # User operations
    def add_user(self, username: str, email: str = None) -> int:
        """Add a new user"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO users (username, email) 
                VALUES (?, ?)
            """, (username, email))
            conn.commit()
            return cursor.lastrowid
    
    def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    # Chat session operations
    def create_chat_session(self, paper_id: int, user_id: int = None, session_name: str = None) -> int:
        """Create a new chat session"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO chat_sessions (paper_id, user_id, session_name) 
                VALUES (?, ?, ?)
            """, (paper_id, user_id, session_name))
            conn.commit()
            return cursor.lastrowid
    
    def get_chat_session(self, session_id: int) -> Optional[Dict[str, Any]]:
        """Get chat session by ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM chat_sessions WHERE id = ?", (session_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_paper_chat_sessions(self, paper_id: int) -> List[Dict[str, Any]]:
        """Get all chat sessions for a paper"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM chat_sessions 
                WHERE paper_id = ? 
                ORDER BY last_activity DESC
            """, (paper_id,))
            return [dict(row) for row in cursor.fetchall()]
    
    def update_session_activity(self, session_id: int):
        """Update session last activity timestamp"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE chat_sessions 
                SET last_activity = CURRENT_TIMESTAMP 
                WHERE id = ?
            """, (session_id,))
            conn.commit()
    
    # Chat message operations
    def add_chat_message(self, session_id: int, role: str, content: str, metadata: Dict[str, Any] = None) -> int:
        """Add a chat message"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            metadata_json = json.dumps(metadata) if metadata else None
            cursor.execute("""
                INSERT INTO chat_messages (session_id, role, content, metadata) 
                VALUES (?, ?, ?, ?)
            """, (session_id, role, content, metadata_json))
            conn.commit()
            
            # Update session activity
            self.update_session_activity(session_id)
            return cursor.lastrowid
    
    def get_chat_messages(self, session_id: int, limit: int = None) -> List[Dict[str, Any]]:
        """Get chat messages for a session"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            query = """
                SELECT * FROM chat_messages 
                WHERE session_id = ? 
                ORDER BY timestamp ASC
            """
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query, (session_id,))
            messages = []
            for row in cursor.fetchall():
                message = dict(row)
                if message['metadata']:
                    message['metadata'] = json.loads(message['metadata'])
                messages.append(message)
            return messages
    
    def get_recent_chat_messages(self, session_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent chat messages for a session"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM chat_messages 
                WHERE session_id = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            """, (session_id, limit))
            messages = []
            for row in reversed(cursor.fetchall()):  # Reverse to get chronological order
                message = dict(row)
                if message['metadata']:
                    message['metadata'] = json.loads(message['metadata'])
                messages.append(message)
            return messages
    
    def delete_chat_session(self, session_id: int) -> bool:
        """Delete a chat session and all its messages"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM chat_sessions WHERE id = ?", (session_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    # Migration utilities
    def migrate_from_json(self, json_file_path: str = "data/papers.json") -> int:
        """Migrate data from JSON file to database"""
        if not os.path.exists(json_file_path):
            return 0
        
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                papers_data = json.load(f)
            
            migrated_count = 0
            for paper in papers_data:
                # Check if paper already exists
                existing = self.check_duplicate_paper(
                    paper.get('title', ''), 
                    paper.get('authors', '')
                )
                if not existing:
                    self.add_paper(paper)
                    migrated_count += 1
            
            return migrated_count
        except Exception as e:
            print(f"Migration error: {e}")
            return 0
    
    def export_to_json(self, output_file: str = "data/papers_backup.json") -> bool:
        """Export papers to JSON file for backup"""
        try:
            papers = self.get_all_papers()
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(papers, f, ensure_ascii=False, indent=2, default=str)
            return True
        except Exception as e:
            print(f"Export error: {e}")
            return False
    
    # Database utilities
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            stats = {}
            
            cursor.execute("SELECT COUNT(*) FROM papers")
            stats['papers_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM users")
            stats['users_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM chat_sessions")
            stats['sessions_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM chat_messages")
            stats['messages_count'] = cursor.fetchone()[0]
            
            return stats
    
    def cleanup_old_sessions(self, days: int = 30) -> int:
        """Clean up old inactive chat sessions"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                DELETE FROM chat_sessions 
                WHERE last_activity < datetime('now', '-{} days')
            """.format(days))
            conn.commit()
            return cursor.rowcount


# Global database instance
db_manager = DatabaseManager() 