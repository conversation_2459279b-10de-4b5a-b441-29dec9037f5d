:root {
    --primary-color: #d1778f;
    --secondary-color: #b96a7e;
    --accent-color: #f09393;
    --gradient-1: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-2: linear-gradient(135deg, var(--accent-color) 0%, #f5576c 100%);
    --dark-color: #333333;
    --text-light: #666666;
    --light-bg: #fdf8f9;
    --border-light: #f0e6e9;
    --success-color: #7bb3d9;
    --success-hover: #6ba5d1;
    --success-active: #5a97c9;
    --blue-gradient: linear-gradient(135deg, #7bb3d9 0%, #6ba5d1 100%);
    --success-bg: #e6f3ff;
    --success-text: #1e5a96;
    --error-color: #dc2626;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: linear-gradient(to bottom, #fff5f5 0%, #ffffff 100%);
    color: #333333;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    background: #ffffff;
}

/* 左侧功能栏 */
.sidebar {
    width: 280px;
    background: #fdf8f9;
    border-right: 1px solid #f0e6e9;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid #f0e6e9;
}

.app-logo {
    text-align: center;
}

.app-name {
    font-size: 24px;
    font-weight: 700;
    color: #333333;
    margin-bottom: 4px;
    background: linear-gradient(135deg, #d1778f 0%, #b96a7e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.app-name-chinese {
    font-size: 14px;
    color: #666666;
    font-weight: 400;
}

.sidebar-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.back-button {
    width: 100%;
    background: #f8f3f4;
    border: 1px solid #f0e6e9;
    border-radius: 8px;
    padding: 12px 16px;
    color: #555555;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
}

.back-button:hover {
    background: #f3e8eb;
    transform: translateY(-1px);
}

.back-icon {
    font-size: 16px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: #ffffff;
    overflow: hidden;
    display: flex;
    flex-direction: row;
}

/* 论文库页面 */
.library-container {
    height: 100vh;
    overflow-y: auto;
    background: #ffffff;
}

.library-header {
    background: #ffffff;
    padding: 40px;
    border-bottom: 1px solid #e2e8f0;
}

.header-content {
    text-align: center;
    margin-bottom: 32px;
}

.library-title {
    font-size: 36px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.title-icon {
    font-size: 36px;
}

.library-subtitle {
    font-size: 16px;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

/* 添加论文区域 */
.add-paper-section {
    margin-top: 32px;
}

.upload-options {
    display: flex;
    gap: 24px;
    justify-content: center;
    flex-wrap: wrap;
}

.upload-option {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.upload-btn {
    background: #d1778f;
    border: none;
    border-radius: 12px;
    padding: 16px 24px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.3);
}

.upload-btn:hover {
    background: #b96a7e;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(209, 119, 143, 0.4);
}

.btn-icon {
    font-size: 18px;
}

.arxiv-input-container {
    display: flex;
    gap: 12px;
    align-items: center;
}

.arxiv-input {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #ffffff;
    color: #1f2937;
    font-size: 14px;
    min-width: 250px;
    transition: border-color 0.2s ease;
}

.arxiv-input:focus {
    outline: none;
    border-color: #d1778f;
    box-shadow: 0 0 0 3px rgba(209, 119, 143, 0.1);
}

.arxiv-input::placeholder {
    color: #9ca3af;
}

.add-arxiv-btn {
    background: var(--success-color);
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.add-arxiv-btn:hover {
    background: var(--success-hover);
    transform: scale(1.05);
}

/* 论文卡片网格 */
.papers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    padding: 40px;
}

.paper-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 16px 20px 20px 20px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: auto;
    opacity: 1;
    transform: translateY(0);
}

.paper-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.card-header-right {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
}

.paper-source {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}



.top-row {
    display: flex;
    align-items: center;
    gap: 4px;
}

.pdf-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.pdf-available {
    background: var(--success-bg);
    color: var(--success-text);
}

.pdf-unavailable {
    background: #fef2f2;
    color: #dc2626;
}

.paper-date-bottom {
    font-size: 11px;
    color: #9ca3af;
    text-align: right;
    margin-top: auto;
    margin-bottom: 8px;
}

.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
}

.paper-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
    line-height: 1.4;
    max-height: 4.2em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    text-overflow: ellipsis;
}

.paper-authors {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 12px;
    max-height: 4.2em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
    text-overflow: ellipsis;
}

.paper-abstract {
    font-size: 14px;
    color: #4b5563;
    line-height: 1.5;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    max-height: 5.8em;
    word-wrap: break-word;
    hyphens: auto;
    margin-bottom: 12px;
    text-overflow: ellipsis;
}

.card-footer {
    border-top: 1px solid #f3f4f6;
    padding-top: 12px;
}

.card-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.chat-button {
    background: #d1778f;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
}

.chat-button:hover {
    background: #b96a7e;
    transform: scale(1.05);
}

.chat-icon {
    font-size: 14px;
}

.pre-read-button {
    background: var(--success-hover);
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
}

.pre-read-button:hover {
    background: var(--success-active);
    transform: scale(1.05);
}

.pre-read-icon {
    font-size: 14px;
}

.delete-button {
    background: #ef4444;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
}

.delete-button:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.delete-icon {
    font-size: 14px;
}

/* 三点菜单样式 */
.card-menu {
    position: relative;
}

.menu-trigger {
    background: transparent;
    border: none;
    border-radius: 6px;
    padding: 6px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-trigger:hover {
    background: #f3f4f6;
    color: #374151;
}

.dots {
    font-size: 16px;
    font-weight: bold;
    transform: rotate(90deg);
    line-height: 1;
}

.menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    min-width: 120px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.menu-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 10px 14px;
    background: transparent;
    border: none;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    text-align: left;
}

.menu-item:hover {
    background: #f8fafc;
}

.menu-item.delete-item {
    color: #dc2626;
}

.menu-item.delete-item:hover {
    background: #fef2f2;
    color: #b91c1c;
}

.menu-icon {
    font-size: 14px;
}

/* 预阅读访问卡片 */
.pre-read-access-card {
    background: var(--blue-gradient);
    border-radius: 12px;
    padding: 16px;
    margin-top: 16px;
    color: white;
}

.pre-read-access-card .info-title {
    color: white;
    margin-bottom: 8px;
}

.pre-read-desc {
    font-size: 13px;
    opacity: 0.9;
    margin-bottom: 12px;
}

.pre-read-access-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    width: 100%;
    justify-content: center;
}

.pre-read-access-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 流式消息样式 */
.streaming-content {
    position: relative;
}

.streaming-content::after {
    content: '▋';
    animation: blink 1s infinite;
    color: #667eea;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 当流式响应完成时移除闪烁光标 */
.message.completed .streaming-content::after {
    display: none;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 24px;
    opacity: 0.5;
}

.empty-title {
    color: #1f2937;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
}

.empty-message {
    color: #6b7280;
    font-size: 16px;
    max-width: 400px;
    margin: 0 auto;
}

/* 聊天界面 */
.chat-panel {
    width: 50%;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-right: 1px solid #e2e8f0;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0 20px;
}

.chat-header {
    padding: 20px 0;
    border-bottom: 1px solid var(--border-light);
    text-align: center;
    width: 100%;
}

.chat-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 4px;
}

.chat-subtitle {
    font-size: 14px;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.4;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message {
    display: flex;
    gap: 12px;
    max-width: 85%;
}

.message.user {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: #d1778f;
    color: white;
}

.message.assistant .message-avatar {
    background: #f8f3f4;
    color: #333333;
}

.message-content {
    background: #fdf8f9;
    border: 1px solid #f0e6e9;
    border-radius: 16px;
    padding: 12px 16px;
    color: #333333;
    line-height: 1.5;
    font-size: 14px;
}

.message.user .message-content {
    background: #d1778f;
    color: white;
    border-color: #d1778f;
}

.message.assistant.error-message .message-content {
    background: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

/* 输入区域 */
.chat-input-area {
    padding: 20px 0;
    border-top: 1px solid #e2e8f0;
}

.input-container {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.message-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: #ffffff;
    color: #1f2937;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    transition: border-color 0.2s ease;
}

.message-input:focus {
    outline: none;
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(123, 179, 217, 0.1);
}

.message-input::placeholder {
    color: #9ca3af;
}

.send-button {
    background: var(--success-color);
    border: none;
    border-radius: 12px;
    padding: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.send-button:hover {
    background: var(--success-hover);
    transform: scale(1.05);
}

.send-button:disabled {
    background: #d1d5db;
    cursor: not-allowed;
    transform: none;
}

/* PDF面板 */
.pdf-panel {
    width: 50%;
    background: #ffffff;
    display: flex;
    flex-direction: column;
}

.pdf-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.pdf-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.pdf-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pdf-control-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 6px;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.pdf-control-btn:hover {
    background: #e5e7eb;
}

.zoom-level {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.pdf-viewer-container {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.pdf-viewer {
    width: 100%;
    height: 100%;
    border: none;
    background: #ffffff;
    transition: transform 0.2s ease;
}

/* 论文信息 */
.paper-info {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 16px;
    margin-top: 16px;
    position: relative;
}

.info-title {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
}

.paper-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.paper-title-sidebar {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
}

.paper-authors-sidebar {
    font-size: 13px;
    color: #6b7280;
}

.paper-source-sidebar {
    font-size: 12px;
    color: #9ca3af;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #1f2937;
    font-size: 16px;
    font-weight: 500;
}

/* 建议问题 */
.suggested-questions {
    margin-top: 16px;
    display: grid;
    gap: 8px;
}

.questions-title {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.question-suggestion {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 10px 14px;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    text-align: left;
    line-height: 1.4;
}

.question-suggestion:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
}

/* 输入类型指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    background: #9ca3af;
    border-radius: 50%;
    animation: typing 1.4s ease-in-out infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
    }
    
    .chat-panel,
    .pdf-panel {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .chat-panel {
        height: 60vh;
    }
    
    .pdf-panel {
        height: 40vh;
        border-bottom: none;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 260px;
    }
    
    .papers-grid {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .upload-options {
        flex-direction: column;
        align-items: center;
    }
    
    .arxiv-input-container {
        flex-direction: column;
        width: 100%;
    }
    
    .arxiv-input {
        width: 100%;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .chat-panel {
        width: 100%;
        height: 70vh;
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .pdf-panel {
        width: 100%;
        height: 30vh;
        border-bottom: none;
    }
    
    .chat-container {
        padding: 0 15px;
    }
    
    .paper-card {
        padding: 12px 16px 16px 16px;
    }
    
    .paper-title {
        font-size: 16px;
        max-height: 3.2em;
        -webkit-line-clamp: 2;
    }
    
    .paper-abstract {
        max-height: 4.2em;
        -webkit-line-clamp: 3;
    }
    
    .card-actions {
        gap: 8px;
    }
    
    .chat-button,
    .pre-read-button {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 确认对话框 */
.confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.confirm-content {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.confirm-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
}

.confirm-message {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 24px;
    line-height: 1.5;
}

.confirm-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.confirm-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.confirm-btn.primary {
    background: #ef4444;
    color: white;
}

.confirm-btn.primary:hover {
    background: #dc2626;
}

.confirm-btn.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.confirm-btn.secondary:hover {
    background: #e5e7eb;
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

/* 特殊样式 */
.chat-switch-btn {
    margin-top: 10px;
    background: var(--success-hover);
}

/* 文本截断通用样式 */
.text-truncate {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    text-overflow: ellipsis;
}

.text-truncate-2 {
    -webkit-line-clamp: 2;
    max-height: 2.8em;
}

.text-truncate-3 {
    -webkit-line-clamp: 3;
    max-height: 3.8em;
}

.text-truncate-4 {
    -webkit-line-clamp: 4;
    max-height: 5.8em;
}

.empty-icon-large {
    font-size: 60px;
    margin-bottom: 20px;
}

.pre-read-chat-container {
    max-width: 100%;
    margin: 0;
}

.pre-read-page-container {
    height: calc(100vh - 40px);
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.pre-read-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 20px 0;
    line-height: 1.6;
    max-height: calc(100vh - 200px);
    width: 100%;
    max-width: 1000px;
}

.pre-read-content h1 { 
    color: var(--primary-color); 
    margin-top: 0; 
}

.pre-read-content h2 { 
    color: var(--secondary-color); 
    border-bottom: 2px solid #f0e6e9; 
    padding-bottom: 8px; 
}

.pre-read-content h3 { 
    color: var(--accent-color); 
}

.pre-read-content ul, 
.pre-read-content ol { 
    padding-left: 24px; 
}

.pre-read-content li { 
    margin: 8px 0; 
}

.pre-read-content blockquote {
    border-left: 4px solid var(--border-light);
    padding-left: 16px;
    margin: 16px 0;
    color: var(--text-light);
}

.pre-read-content code {
    background: var(--light-bg);
    padding: 2px 4px;
    border-radius: 4px;
}

/* 修复预阅读页面滚动问题的通用样式 */
.chat-container.pre-read-chat-container {
    height: auto !important;
    max-height: 100vh;
    overflow-y: auto;
}

/* 确保预阅读内容可以滚动 */
#preReadContent {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 20px 0;
    line-height: 1.6;
}

/* 空状态样式 */
.empty-pre-read {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-light);
    width: 100%;
    max-width: 600px;
}

.generate-pre-read-btn {
    background: var(--gradient-1);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 20px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.3);
}

.generate-pre-read-btn:hover {
    background: var(--gradient-2);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(209, 119, 143, 0.4);
}

.generate-pre-read-btn:hover {
    transform: translateY(-2px);
}

/* 集成的预阅读样式 */
.integrated-preread {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e2e8f0;
}

.preread-actions {
    margin-bottom: 6px;
}

.preread-btn {
    background: var(--gradient-1);
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    width: 100%;
    justify-content: center;
}

.preread-btn:hover {
    background: var(--gradient-2);
    transform: translateY(-1px);
}

.preread-hint {
    font-size: 11px;
    color: #6b7280;
    text-align: center;
    margin: 0;
}

/* 跨文档分析按钮样式 */
.multi-doc-section {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
    border: 1px solid #f9a8d4;
    border-radius: 12px;
}

.multi-doc-button {
    background: var(--gradient-1);
    border: none;
    border-radius: 10px;
    padding: 12px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    width: 100%;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(209, 119, 143, 0.25);
}

.multi-doc-button:hover {
    background: var(--gradient-2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.35);
}

.multi-doc-icon {
    font-size: 16px;
}

.multi-doc-desc {
    font-size: 12px;
    color: var(--secondary-color);
    text-align: center;
    margin: 8px 0 0 0;
    opacity: 0.8;
}

/* 跨文档分析状态指示 */
.multi-doc-status {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
    border: 1px solid var(--primary-color);
    border-radius: 8px;
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 12px;
}

.status-icon {
    color: var(--primary-color);
}

.status-text {
    color: var(--secondary-color);
    font-weight: 500;
    flex: 1;
}

.exit-multi-doc {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 4px;
    padding: 4px 8px;
    color: #dc2626;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
}

.exit-multi-doc:hover {
    background: rgba(239, 68, 68, 0.2);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: #ffffff;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-desc {
    color: #6b7280;
    margin-bottom: 20px;
    font-size: 14px;
}

.paper-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.paper-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.paper-item:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.paper-item.selected {
    background: #eff6ff;
    border-color: #3b82f6;
}

.paper-checkbox {
    margin: 0;
    cursor: pointer;
}

.paper-item-content {
    flex: 1;
}

.paper-item-title {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 4px;
    line-height: 1.4;
}

.paper-item-meta {
    font-size: 12px;
    color: #6b7280;
}

.already-added {
    color: #10b981;
    font-weight: 500;
    margin-left: 8px;
}

.paper-item.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.paper-item.disabled .paper-checkbox {
    cursor: not-allowed;
}

.preread-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    background: #f3f4f6;
    color: #6b7280;
}

.preread-status.has-formal {
    background: #dcfce7;
    color: #166534;
}

.preread-status.has-hidden {
    background: #fef3c7;
    color: #92400e;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

.modal-btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.modal-btn.secondary {
    background: #ffffff;
    border-color: #d1d5db;
    color: #374151;
}

.modal-btn.secondary:hover {
    background: #f9fafb;
}

.modal-btn.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.modal-btn.primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
}

.modal-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
} 

/* 论文卡片样式 - 所有论文都使用相同的样式 */
.reference-papers-list {
    margin-top: 3px;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.reference-paper-card {
    background: #ffffff;
    border: 1px solid #f0e6e9;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.2s ease;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reference-paper-card:hover {
    background: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.15);
    border-color: var(--primary-color);
}

.reference-paper-card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 8px 0;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

.reference-paper-card-meta {
    font-size: 14px;
    color: var(--text-light);
    margin: 0 0 12px 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

.reference-paper-card-source {
    font-size: 12px;
    color: #999999;
    margin: 0 0 16px 0;
}

.reference-paper-card-preread {
    margin-top: 16px;
}

.reference-paper-card-preread-btn {
    width: 100%;
    background: var(--gradient-1);
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.reference-paper-card-preread-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(209, 119, 143, 0.3);
}

.reference-paper-card-preread-hint {
    font-size: 12px;
    color: var(--text-light);
    margin: 8px 0 0 0;
    text-align: center;
}

.reference-paper-card-remove {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(239, 68, 68, 0.1);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    color: #dc2626;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
}

.reference-paper-card:hover .reference-paper-card-remove,
.paper-info:hover .reference-paper-card-remove {
    opacity: 1;
}

.reference-paper-card-remove:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

/* 置顶按钮样式 */
.reference-paper-card-pin {
    position: absolute;
    top: 8px;
    right: 40px;
    width: 24px;
    height: 24px;
    border: none;
    background: #f3f4f6;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #6b7280;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.reference-paper-card-pin:hover {
    background: #e5e7eb;
    color: #374151;
    opacity: 1;
    transform: scale(1.1);
}

.reference-paper-card-pin.pinned {
    background: #d1778f;
    color: white;
    opacity: 1;
}

.reference-paper-card-pin.pinned:hover {
    background: #b96a7e;
    transform: scale(1.1);
}

/* 当前论文样式 */
.info-title.current-paper {
    color: #d1778f;
    font-weight: 600;
}

.paper-info.current-paper {
    border: 2px solid #d1778f;
    background: linear-gradient(135deg, #fdf8f9 0%, #f8f3f4 100%);
}

/* 通知消息样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideInRight 0.3s ease;
    max-width: 300px;
    word-wrap: break-word;
}

.notification.success {
    background: #10b981;
}

.notification.error {
    background: #dc2626;
}

.notification.info {
    background: #3b82f6;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
} 