document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const chatMessages = document.getElementById('chatMessages');
    
    // 对话历史存储
    let chatHistory = [];
    
    // 多文档模式相关变量
    let multiDocMode = false;
    let selectedPapers = [];
    let activePapers = [];
    let papersData = [];
    let currentPaperId = paperId; // 当前论文ID，默认为初始论文

    // 增强的Markdown渲染函数
    function renderMarkdown(text) {
        if (typeof text !== 'string') return text;
        
        return text
            // 代码块 (先处理，避免被其他规则影响)
            .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            
            // 粗体
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/__(.*?)__/g, '<strong>$1</strong>')
            
            // 斜体
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/_(.*?)_/g, '<em>$1</em>')
            
            // 行内代码
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            
            // 标题 (支持 # ## ### 等)
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            
            // 无序列表
            .replace(/^\* (.*$)/gm, '<li>$1</li>')
            .replace(/^- (.*$)/gm, '<li>$1</li>')
            .replace(/^(\d+)\. (.*$)/gm, '<li class="numbered">$2</li>')
            
            // 链接
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
            
            // 换行
            .replace(/\n/g, '<br>');
    }

    // 后处理：包装列表项
    function postProcessMarkdown(html) {
        // 将连续的<li>包装在<ul>中
        html = html.replace(/(<li(?:\s+class="[^"]*")?>[^<]*<\/li>(?:<br>)*)+/g, function(match) {
            const items = match.replace(/<br>/g, '');
            if (items.includes('class="numbered"')) {
                return '<ol>' + items.replace(/class="numbered"/g, '') + '</ol>';
            } else {
                return '<ul>' + items + '</ul>';
            }
        });
        
        // 清理多余的<br>标签
        html = html.replace(/<br><\/ul>/g, '</ul>');
        html = html.replace(/<br><\/ol>/g, '</ol>');
        html = html.replace(/<\/h[1-6]><br>/g, function(match) {
            return match.replace('<br>', '');
        });
        
        return html;
    }

    // 发送消息函数（支持单文档和多文档模式）
    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;

        // 添加用户消息
        addMessage(message, 'user');
        chatHistory.push({ role: 'user', content: message });
        
        // 清空输入框
        messageInput.value = '';

        // 创建助手消息容器
        const assistantMessageDiv = document.createElement('div');
        assistantMessageDiv.className = 'message assistant';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = '<div class="streaming-content"></div>';
        
        assistantMessageDiv.appendChild(avatar);
        assistantMessageDiv.appendChild(messageContent);
        chatMessages.appendChild(assistantMessageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        const streamingContent = messageContent.querySelector('.streaming-content');
        let fullResponse = '';

        // 根据模式选择API端点和请求体
        let apiEndpoint, requestBody;
        
        if (multiDocMode && activePapers && activePapers.length > 0) {
            // 跨文档分析模式 - 使用流式API
            apiEndpoint = '/chat_multi_doc_stream';
            
            // 构建参考论文列表（排除当前论文）
            const referencePaperIds = activePapers.filter(id => id !== currentPaperId);
            
            requestBody = {
                message: message,
                main_paper_id: currentPaperId, // 使用当前论文作为主论文
                reference_paper_ids: referencePaperIds,
                chat_history: chatHistory
            };
        } else {
            // 普通单文档模式 - 使用流式API
            apiEndpoint = '/chat_stream';
            requestBody = {
                message: message,
                paper_id: paperId,
                chat_history: chatHistory
            };
        }

        // 使用流式API调用
        fetch(apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            
            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            
            function readChunk() {
                return reader.read().then(({ done, value }) => {
                    if (done) {
                        // 流式响应完成
                        assistantMessageDiv.classList.add('completed');
                        chatHistory.push({ role: 'assistant', content: fullResponse });
                        return;
                    }
                    
                    // 解析流式数据
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                if (data.type === 'content' && data.content) {
                                    fullResponse += data.content;
                                    const processedContent = postProcessMarkdown(renderMarkdown(fullResponse));
                                    streamingContent.innerHTML = processedContent;
                                    chatMessages.scrollTop = chatMessages.scrollHeight;
                                } else if (data.type === 'error') {
                                    streamingContent.innerHTML = renderMarkdown(data.content || '出现错误');
                                    assistantMessageDiv.classList.add('error-message');
                                    return;
                                } else if (data.type === 'done') {
                                    assistantMessageDiv.classList.add('completed');
                                    chatHistory.push({ role: 'assistant', content: fullResponse });
                                    return;
                                }
                            } catch (e) {
                                // 忽略JSON解析错误
                            }
                        }
                    }
                    
                    // 继续读取下一个块
                    return readChunk();
                });
            }
            
            return readChunk();
        })
        .catch(error => {
            console.error('Stream error:', error);
            streamingContent.innerHTML = '网络连接错误，请检查网络连接后重试。';
            assistantMessageDiv.classList.add('error-message');
        });
    }

    // 添加消息到聊天区域
    function addMessage(content, sender, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        if (isError) messageDiv.classList.add('error-message');
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = sender === 'user' ? '👤' : '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // 处理消息内容格式
        if (typeof content === 'string') {
            // 应用Markdown渲染
            let formattedContent = renderMarkdown(content);
            formattedContent = postProcessMarkdown(formattedContent);
            messageContent.innerHTML = formattedContent;
        } else {
            messageContent.textContent = content;
        }
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        chatMessages.appendChild(messageDiv);
        
        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        return messageDiv;
    }

    // 添加加载消息
    function addLoadingMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant loading-message';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = `
            <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
            </div>
        `;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        return messageDiv;
    }

    // 移除加载消息
    function removeLoadingMessage(loadingMessage) {
        if (loadingMessage && loadingMessage.parentNode) {
            loadingMessage.parentNode.removeChild(loadingMessage);
        }
    }

    // 绑定事件
    sendButton.addEventListener('click', sendMessage);
    
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 自动聚焦输入框
    messageInput.focus();
    
    // 添加一些示例问题按钮
    addSuggestedQuestions();
});

// 添加建议问题
function addSuggestedQuestions() {
    const welcomeMessage = document.querySelector('.welcome-message');
    if (welcomeMessage) {
        const suggestedQuestions = [
            "这篇论文的主要贡献是什么？",
            "请解释论文的核心方法",
            "这篇论文有哪些创新点？",
            "论文的实验结果如何？"
        ];
        
        const questionsContainer = document.createElement('div');
        questionsContainer.className = 'suggested-questions';
        questionsContainer.innerHTML = '<p class="questions-title">💡 建议问题：</p>';
        
        suggestedQuestions.forEach(question => {
            const questionBtn = document.createElement('button');
            questionBtn.className = 'question-suggestion';
            questionBtn.textContent = question;
            questionBtn.onclick = () => {
                document.getElementById('messageInput').value = question;
                document.getElementById('messageInput').focus();
            };
            questionsContainer.appendChild(questionBtn);
        });
        
        welcomeMessage.appendChild(questionsContainer);
    }
}

// 返回论文库
function goBack() {
    window.location.href = '/';
}

// PDF缩放功能
let currentZoom = 100;
const minZoom = 50;
const maxZoom = 200;

document.addEventListener('DOMContentLoaded', function() {
    const zoomInBtn = document.getElementById('zoomInBtn');
    const zoomOutBtn = document.getElementById('zoomOutBtn');
    const zoomLevel = document.getElementById('zoomLevel');
    const pdfViewer = document.getElementById('pdfViewer');
    
    // 缩放功能
    function updateZoom() {
        if (pdfViewer) {
            pdfViewer.style.transform = `scale(${currentZoom / 100})`;
            pdfViewer.style.transformOrigin = 'top left';
        }
        if (zoomLevel) {
            zoomLevel.textContent = `${currentZoom}%`;
        }
    }
    
    // 放大
    if (zoomInBtn) {
        zoomInBtn.addEventListener('click', function() {
            if (currentZoom < maxZoom) {
                currentZoom += 10;
                updateZoom();
            }
        });
    }
    
    // 缩小
    if (zoomOutBtn) {
        zoomOutBtn.addEventListener('click', function() {
            if (currentZoom > minZoom) {
                currentZoom -= 10;
                updateZoom();
            }
        });
    }
    
    // 初始化缩放
    updateZoom();
    
    // 将sendMessage函数暴露为全局函数
    window.sendMessage = sendMessage;
});

// 全局函数：跳转到预阅读页面（修复预阅读按钮问题）
function goToPreRead(paperId) {
    window.location.href = `/view_pre_read/${paperId}`;
}

// 全局函数：返回论文库
function goBack() {
    window.location.href = '/';
}

// 全局函数：发送建议问题
function sendSuggestedQuestion(question) {
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.value = question;
        messageInput.focus();
        // 触发发送消息
        if (window.sendMessage) {
            window.sendMessage();
        }
    }
}

// 跨文档分析功能
let multiDocMode = false;
let papersData = []; // 存储所有论文数据
let selectedPapers = [];
let activePapers = []; // 当前激活的跨文档分析中的参考论文ID
let allPapers = [];

// 打开跨文档选择弹窗
function openMultiDocModal() {
    const modal = document.getElementById('multiDocModal');
    if (modal) {
        modal.style.display = 'flex';
        // 重置按钮状态
        const startButton = document.querySelector('.modal-btn.primary');
        if (startButton) {
            startButton.textContent = '开始分析';
            startButton.disabled = selectedPapers.length === 0;
        }
        loadPaperList();
    }
}

// 关闭跨文档选择弹窗
function closeMultiDocModal() {
    const modal = document.getElementById('multiDocModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 加载论文列表
async function loadPaperList() {
    try {
        const response = await fetch('/api/papers');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                papersData = data.papers; // 保存论文数据
                allPapers = data.papers; // 兼容现有代码
                renderPaperList(data.papers);
            } else {
                console.error('Failed to load papers:', data.error);
            }
        } else {
            console.error('Failed to load papers');
        }
    } catch (error) {
        console.error('Error loading papers:', error);
    }
}

// 渲染论文列表
function renderPaperList(papers) {
    const paperList = document.getElementById('paperList');
    if (!paperList) return;

    paperList.innerHTML = '';
    
    // 过滤掉当前论文
    const filteredPapers = papers.filter(paper => paper.id !== paperId);
    
    if (filteredPapers.length === 0) {
        paperList.innerHTML = '<p style="text-align: center; color: #6b7280; font-size: 14px;">暂无其他论文可选择</p>';
        return;
    }

    filteredPapers.forEach(paper => {
        const paperItem = document.createElement('div');
        paperItem.className = 'paper-item';
        paperItem.dataset.paperId = paper.id; // 添加data属性存储论文ID
        paperItem.onclick = () => togglePaperSelection(paper.id);
        
        // 检查是否已经在多论文对话中
        const isAlreadyInAnalysis = multiDocMode && activePapers.includes(paper.id);
        const isSelected = selectedPapers.includes(paper.id);
        
        // 检查预阅读状态
        let prereadStatus = '';
        if (paper.has_formal_preread) {
            prereadStatus = '<span class="preread-status has-formal">📚 正式预阅读</span>';
        } else if (paper.has_hidden_preread) {
            prereadStatus = '<span class="preread-status has-hidden">📄 简化预阅读</span>';
        } else {
            prereadStatus = '<span class="preread-status">⏳ 待生成</span>';
        }

        // 如果已经在对话中，添加禁用状态
        if (isAlreadyInAnalysis) {
            paperItem.classList.add('disabled');
            paperItem.style.opacity = '0.5';
            paperItem.style.pointerEvents = 'none';
        }

        paperItem.innerHTML = `
            <input type="checkbox" class="paper-checkbox" ${isSelected ? 'checked' : ''} ${isAlreadyInAnalysis ? 'disabled' : ''}>
            <div class="paper-item-content">
                <div class="paper-item-title">${paper.title}</div>
                <div class="paper-item-meta">
                    ${paper.authors} • ${paper.source} ${prereadStatus}
                    ${isAlreadyInAnalysis ? '<span class="already-added">✓ 已在对话中</span>' : ''}
                </div>
            </div>
        `;
        
        paperList.appendChild(paperItem);
    });
}

// 切换论文选择状态
function togglePaperSelection(paperId) {
    // 检查是否已经在多论文对话中包含了这篇论文
    if (multiDocMode && activePapers.includes(paperId)) {
        alert('这篇论文已经在对话中了，不能重复添加');
        return;
    }
    
    const index = selectedPapers.indexOf(paperId);
    if (index === -1) {
        selectedPapers.push(paperId);
    } else {
        selectedPapers.splice(index, 1);
    }
    
    // 更新UI
    const paperItems = document.querySelectorAll('.paper-item');
    paperItems.forEach(item => {
        const checkbox = item.querySelector('.paper-checkbox');
        const itemPaperId = parseInt(item.dataset.paperId);
        
        if (itemPaperId && selectedPapers.includes(itemPaperId)) {
            item.classList.add('selected');
            checkbox.checked = true;
        } else {
            item.classList.remove('selected');
            checkbox.checked = false;
        }
        
        // 如果论文已经在多论文对话中，禁用选择
        if (multiDocMode && activePapers.includes(itemPaperId)) {
            item.classList.add('disabled');
            item.style.opacity = '0.5';
            item.style.pointerEvents = 'none';
            checkbox.disabled = true;
        } else {
            item.classList.remove('disabled');
            item.style.opacity = '1';
            item.style.pointerEvents = 'auto';
            checkbox.disabled = false;
        }
    });
    
    // 更新开始分析按钮状态
    const startButton = document.querySelector('.modal-btn.primary');
    if (startButton) {
        startButton.disabled = selectedPapers.length === 0;
        // 确保按钮文本正确
        if (startButton.textContent === '正在初始化...') {
            startButton.textContent = '开始分析';
        }
    }
}

// 开始跨文档分析
async function startMultiDocAnalysis() {
    if (selectedPapers.length === 0) {
        alert('请至少选择一篇论文');
        return;
    }

    try {
        // 显示加载状态
        const startButton = document.querySelector('.modal-btn.primary');
        const originalText = startButton.textContent;
        startButton.textContent = '正在添加...';
        startButton.disabled = true;

        // 调用后端API添加论文到对话
        const response = await fetch('/api/start_multi_doc_analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                main_paper_id: paperId,
                reference_paper_ids: selectedPapers
            })
        });

        if (response.ok) {
            const result = await response.json();
            
            // 激活跨文档模式（如果还没有激活）
            if (!multiDocMode) {
                multiDocMode = true;
                activePapers = []; // 初始化空数组
                currentPaperId = paperId; // 初始论文作为当前论文
            }
            
            // 添加新选中的论文到现有列表中（避免重复）
            selectedPapers.forEach(paperId => {
                if (!activePapers.includes(paperId)) {
                    activePapers.push(paperId);
                }
            });
            
            // 显示跨文档状态指示
            const statusDiv = document.getElementById('multiDocStatus');
            if (statusDiv) {
                statusDiv.style.display = 'flex';
                statusDiv.querySelector('.status-text').textContent = 
                    `多论文对话模式 (${activePapers.length + 1}篇论文)`;
            }
            
            // 显示参考论文卡片
            displayReferencePaperCards(activePapers);
            
            // 更新聊天界面提示
            updateChatInterface();
            
            // 关闭弹窗
            closeMultiDocModal();
            
            // 重置选择（用于下次选择）
            selectedPapers = [];
            
        } else {
            const error = await response.json();
            alert('添加失败：' + (error.error || '未知错误'));
        }
    } catch (error) {
        console.error('Error adding papers to conversation:', error);
        alert('添加失败，请稍后重试');
    } finally {
        // 恢复按钮状态
        const startButton = document.querySelector('.modal-btn.primary');
        if (startButton) {
            startButton.textContent = '添加论文';
            startButton.disabled = false;
        }
    }
}

// 显示论文卡片
function displayReferencePaperCards(paperIds) {
    const papersList = document.getElementById('referencePapersList');
    
    if (!papersList) return;
    
    // 清空现有内容
    papersList.innerHTML = '';
    
    if (paperIds.length === 0) {
        return;
    }
    
    // 根据paperIds顺序显示论文卡片
    paperIds.forEach((paperId, index) => {
        const paper = papersData.find(p => p.id === paperId);
        if (paper) {
            const cardElement = createReferencePaperCard(paper, index);
            papersList.appendChild(cardElement);
        }
    });
}

// 创建论文卡片
function createReferencePaperCard(paper, index) {
    const card = document.createElement('div');
    card.className = `paper-info ${paper.id === currentPaperId ? 'current-paper' : ''}`;
    card.dataset.paperId = paper.id;
    
    // 检查论文是否有PDF
    const hasPdf = paper.has_pdf !== false; // 默认为true，除非明确设置为false
    
    // 检查是否为当前论文
    const isCurrentPaper = paper.id === currentPaperId;
    const currentPaperTitle = isCurrentPaper ? '当前论文' : '参考论文';
    
    card.innerHTML = `
        <button class="reference-paper-card-remove" onclick="removeReferencePaper(${paper.id})" title="移除此论文">
            ×
        </button>
        <button class="reference-paper-card-pin ${isCurrentPaper ? 'pinned' : ''}" onclick="switchCurrentPaper(${paper.id})" title="${isCurrentPaper ? '当前论文' : '设为当前论文'}">
            📌
        </button>
        <h3 class="info-title ${isCurrentPaper ? 'current-paper' : ''}">${currentPaperTitle}</h3>
        <div class="paper-details">
            <p class="paper-title-sidebar">${paper.title}</p>
            <p class="paper-authors-sidebar">${paper.authors}</p>
            <p class="paper-source-sidebar">${paper.source}</p>
        </div>
        
        <!-- 集成的预阅读功能 -->
        ${hasPdf ? `
        <div class="integrated-preread">
            <div class="preread-actions">
                <button class="preread-btn" onclick="goToPreRead(${paper.id})">
                    <span class="btn-icon">📚</span>
                    查看预阅读
                </button>
            </div>
            <p class="preread-hint">AI深度分析论文内容</p>
        </div>
        ` : ''}
    `;
    
    return card;
}

// 移除论文
function removeReferencePaper(paperId) {
    if (!multiDocMode) return;
    
    // 从激活的论文列表中移除
    const index = activePapers.indexOf(paperId);
    if (index > -1) {
        activePapers.splice(index, 1);
    }
    
    // 如果移除的是当前论文，需要切换到其他论文
    if (paperId === currentPaperId) {
        if (activePapers.length > 0) {
            // 切换到第一篇可用的论文
            switchCurrentPaper(activePapers[0]);
        } else {
            // 如果没有其他论文了，退出多论文模式
            exitMultiDocMode();
            return;
        }
    }
    
    // 更新显示
    displayReferencePaperCards(activePapers);
    
    // 更新状态指示
    const statusDiv = document.getElementById('multiDocStatus');
    if (statusDiv) {
        if (activePapers.length === 0) {
            // 如果没有其他论文了，退出多论文模式
            exitMultiDocMode();
        } else {
            statusDiv.querySelector('.status-text').textContent = 
                `多论文对话模式 (${activePapers.length + 1}篇论文)`;
        }
    }
    
    // 重新渲染论文列表，使被移除的论文可以重新选择
    if (papersData.length > 0) {
        renderPaperList(papersData);
    }
}

// 切换当前论文
async function switchCurrentPaper(paperId) {
    if (paperId === currentPaperId) {
        return; // 已经是当前论文，无需切换
    }
    
    try {
        // 显示加载状态
        const pinButton = document.querySelector(`[onclick="switchCurrentPaper(${paperId})"]`);
        if (pinButton) {
            pinButton.style.opacity = '0.5';
            pinButton.disabled = true;
        }
        
        // 调用后端API切换当前论文
        const response = await fetch('/api/switch_current_paper', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                paper_id: paperId
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            
            // 更新当前论文ID
            currentPaperId = paperId;
            
            // 更新PDF显示
            updatePdfDisplay(paperId);
            
            // 更新当前论文信息显示
            updateCurrentPaperInfo(paperId);
            
            // 更新论文卡片显示
            displayReferencePaperCards(activePapers);
            
            // 更新聊天界面提示
            updateChatInterface();
            
            // 显示成功消息
            showNotification(result.message, 'success');
            
        } else {
            const error = await response.json();
            showNotification('切换失败：' + (error.error || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('Error switching current paper:', error);
        showNotification('切换失败，请稍后重试', 'error');
    } finally {
        // 恢复按钮状态
        const pinButton = document.querySelector(`[onclick="switchCurrentPaper(${paperId})"]`);
        if (pinButton) {
            pinButton.style.opacity = '1';
            pinButton.disabled = false;
        }
    }
}

// 更新PDF显示
function updatePdfDisplay(paperId) {
    const pdfViewer = document.getElementById('pdfViewer');
    if (pdfViewer) {
        pdfViewer.src = `/view_pdf/${paperId}`;
    }
}

// 更新当前论文信息显示
function updateCurrentPaperInfo(paperId) {
    const currentPaperInfo = document.getElementById('currentPaperInfo');
    if (!currentPaperInfo) return;
    
    const paper = papersData.find(p => p.id === paperId);
    if (!paper) return;
    
    const titleElement = currentPaperInfo.querySelector('.paper-title-sidebar');
    const authorsElement = currentPaperInfo.querySelector('.paper-authors-sidebar');
    const sourceElement = currentPaperInfo.querySelector('.paper-source-sidebar');
    const prereadBtn = currentPaperInfo.querySelector('.preread-btn');
    
    if (titleElement) titleElement.textContent = paper.title;
    if (authorsElement) authorsElement.textContent = paper.authors;
    if (sourceElement) sourceElement.textContent = paper.source;
    
    // 更新预阅读按钮的onclick事件
    if (prereadBtn) {
        prereadBtn.onclick = () => goToPreRead(paperId);
    }
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 退出多论文模式
function exitMultiDocMode() {
    multiDocMode = false;
    selectedPapers = [];
    activePapers = [];
    currentPaperId = paperId; // 重置为初始论文
    
    // 隐藏状态指示
    const statusDiv = document.getElementById('multiDocStatus');
    if (statusDiv) {
        statusDiv.style.display = 'none';
    }
    
    // 清空论文卡片
    const papersList = document.getElementById('referencePapersList');
    if (papersList) {
        papersList.innerHTML = '';
    }
    
    // 恢复PDF显示
    updatePdfDisplay(paperId);
    
    // 恢复聊天界面
    updateChatInterface();
}

// 更新聊天界面
function updateChatInterface() {
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        if (multiDocMode) {
            messageInput.placeholder = '多论文对话提问...';
            
            // 获取当前论文信息
            const currentPaper = papersData.find(p => p.id === currentPaperId) || 
                                { title: '未知论文', authors: '未知作者' };
            
            // 添加多论文对话的欢迎消息
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                const assistantMessage = welcomeMessage.querySelector('.message.assistant .message-content');
                if (assistantMessage) {
                    assistantMessage.innerHTML = `
                        您好！我现在可以进行多论文对话了。当前以 <strong>${currentPaper.title}</strong> 为主要视角，结合其他参考论文来回答您的问题。您可以询问关于这些论文的对比分析问题，比如：
                        
                        <div class="suggested-questions">
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('请以当前论文为主要视角，对比这些论文的主要方法有什么异同？')">
                                请以当前论文为主要视角，对比这些论文的主要方法有什么异同？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('当前论文相比其他论文有什么独特之处？')">
                                当前论文相比其他论文有什么独特之处？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('这些研究之间有什么关联性？')">
                                这些研究之间有什么关联性？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('综合这些论文，该领域的发展趋势是什么？')">
                                综合这些论文，该领域的发展趋势是什么？
                            </button>
                        </div>
                    `;
                }
            }
        } else {
            messageInput.placeholder = '向论文提问...';
            
            // 恢复原始欢迎消息
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                const assistantMessage = welcomeMessage.querySelector('.message.assistant .message-content');
                if (assistantMessage) {
                    assistantMessage.innerHTML = `
                        您好！我是您的论文阅读助手。我已经了解了这篇论文的内容，您可以向我询问关于这篇论文的任何问题，比如：
                        
                        <div class="suggested-questions">
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('这篇论文的主要贡献是什么？')">
                                这篇论文的主要贡献是什么？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('请解释一下论文中的核心方法')">
                                请解释一下论文中的核心方法
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('这篇论文有哪些局限性？')">
                                这篇论文有哪些局限性？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('相关工作有哪些？')">
                                相关工作有哪些？
                            </button>
                        </div>
                    `;
                }
            }
        }
    }
}