#!/usr/bin/env python3
"""
Test Flask app startup and basic functionality
"""

import sys
import os
import time
import requests
import threading
sys.path.append('.')

def test_app_startup():
    """Test if Flask app can start without errors"""
    print("测试Flask应用启动...")
    
    try:
        # 导入应用
        from app import app, db_manager, get_rag_service
        print("✓ 应用导入成功")
        
        # 测试数据库连接
        papers = db_manager.get_all_papers()
        print(f"✓ 数据库连接正常，共有 {len(papers)} 篇论文")
        
        # 测试RAG服务
        rag_svc = get_rag_service()
        if rag_svc:
            print("✓ RAG服务初始化成功")
        else:
            print("⚠ RAG服务初始化失败，将使用普通对话模式")
        
        # 测试基本路由
        with app.test_client() as client:
            # 测试主页
            response = client.get('/')
            print(f"✓ 主页访问正常 (状态码: {response.status_code})")
            
            # 测试API端点
            response = client.get('/api/papers')
            if response.status_code == 200:
                data = response.get_json()
                print(f"✓ 论文API正常，返回 {len(data.get('papers', []))} 篇论文")
            else:
                print(f"✗ 论文API错误 (状态码: {response.status_code})")
            
            # 测试统计API
            response = client.get('/api/database_stats')
            if response.status_code == 200:
                data = response.get_json()
                print(f"✓ 统计API正常: {data.get('stats', {})}")
            else:
                print(f"✗ 统计API错误 (状态码: {response.status_code})")
        
        return True
        
    except Exception as e:
        print(f"✗ 应用启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_api():
    """Test chat API functionality"""
    print("\n测试聊天API功能...")
    
    try:
        from app import app, db_manager
        
        # 获取一篇论文进行测试
        papers = db_manager.get_all_papers()
        if not papers:
            print("✗ 没有论文可供测试")
            return False
        
        test_paper = papers[0]
        print(f"使用论文进行测试: {test_paper['title'][:50]}...")
        
        with app.test_client() as client:
            # 测试聊天API
            chat_data = {
                "message": "这篇论文的主要内容是什么？",
                "paper_id": test_paper['id'],
                "user_id": 1
            }
            
            response = client.post('/chat_api', 
                                 json=chat_data,
                                 content_type='application/json')
            
            if response.status_code == 200:
                data = response.get_json()
                if data.get('success'):
                    print("✓ 聊天API正常工作")
                    print(f"  AI回复: {data.get('response', '')[:100]}...")
                    print(f"  会话ID: {data.get('session_id')}")
                else:
                    print(f"✗ 聊天API返回错误: {data.get('error')}")
            else:
                print(f"✗ 聊天API HTTP错误 (状态码: {response.status_code})")
                print(f"  响应: {response.get_data(as_text=True)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 聊天API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("Flask应用完整功能测试")
    print("=" * 60)
    
    success = True
    
    # 测试应用启动
    if not test_app_startup():
        success = False
    
    # 测试聊天API
    if not test_chat_api():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 所有测试通过！应用功能正常")
    else:
        print("✗ 部分测试失败，请检查错误信息")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
