#!/usr/bin/env python3
"""
Migration script for DocuMancer
Migrates data from JSON files to SQLite database
"""

import os
import json
import sys
from datetime import datetime
from database import DatabaseManager


def migrate_papers_data():
    """
    Migrate papers data from JSON file to database
    """
    print("开始迁移论文数据...")
    
    db = DatabaseManager()
    
    # 备份现有数据库数据
    backup_file = f"data/papers_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    if db.export_to_json(backup_file):
        print(f"数据库备份已保存到: {backup_file}")
    
    # 迁移JSON数据
    json_file = "data/papers.json"
    if os.path.exists(json_file):
        migrated_count = db.migrate_from_json(json_file)
        print(f"成功迁移 {migrated_count} 篇论文")
        
        # 备份原JSON文件
        backup_json = f"data/papers_json_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.rename(json_file, backup_json)
        print(f"原JSON文件已备份为: {backup_json}")
    else:
        print("未找到papers.json文件，跳过迁移")
    
    # 显示数据库统计信息
    stats = db.get_database_stats()
    print("\n数据库统计信息:")
    print(f"  论文数量: {stats['papers_count']}")
    print(f"  用户数量: {stats['users_count']}")
    print(f"  会话数量: {stats['sessions_count']}")
    print(f"  消息数量: {stats['messages_count']}")


def create_default_user():
    """
    Create a default user for the system
    """
    print("\n创建默认用户...")
    
    db = DatabaseManager()
    
    # 检查是否已有用户
    existing_user = db.get_user_by_username("default")
    if existing_user:
        print("默认用户已存在")
        return existing_user['id']
    
    # 创建默认用户
    user_id = db.add_user("default", "<EMAIL>")
    print(f"默认用户创建成功，ID: {user_id}")
    return user_id


def test_database_operations():
    """
    Test basic database operations
    """
    print("\n测试数据库操作...")
    
    db = DatabaseManager()
    
    try:
        # 测试论文操作
        papers = db.get_all_papers()
        print(f"数据库中共有 {len(papers)} 篇论文")
        
        if papers:
            # 测试获取单篇论文
            paper = db.get_paper(papers[0]['id'])
            print(f"测试获取论文: {paper['title'][:50]}...")
        
        # 测试用户操作
        users = db.get_user_by_username("default")
        if users:
            print(f"默认用户ID: {users['id']}")
        
        print("数据库操作测试成功！")
        
    except Exception as e:
        print(f"数据库操作测试失败: {e}")
        return False
    
    return True


def verify_data_integrity():
    """
    Verify data integrity after migration
    """
    print("\n验证数据完整性...")
    
    db = DatabaseManager()
    
    try:
        # 检查重复数据
        papers = db.get_all_papers()
        titles = [paper['title'] for paper in papers]
        unique_titles = set(titles)
        
        if len(titles) != len(unique_titles):
            print(f"警告: 发现重复标题 ({len(titles) - len(unique_titles)} 个重复)")
        else:
            print("未发现重复论文")
        
        # 检查文件路径
        missing_files = 0
        for paper in papers:
            if paper['file_path'] and not os.path.exists(paper['file_path']):
                missing_files += 1
        
        if missing_files > 0:
            print(f"警告: {missing_files} 个PDF文件路径无效")
        else:
            print("所有文件路径有效")
        
        print("数据完整性验证完成")
        return True
        
    except Exception as e:
        print(f"数据完整性验证失败: {e}")
        return False


def main():
    """
    Main migration function
    """
    print("=" * 50)
    print("DocuMancer 数据库迁移工具")
    print("=" * 50)
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    try:
        # 步骤1: 迁移论文数据
        migrate_papers_data()
        
        # 步骤2: 创建默认用户
        create_default_user()
        
        # 步骤3: 测试数据库操作
        if not test_database_operations():
            print("数据库测试失败，请检查配置")
            sys.exit(1)
        
        # 步骤4: 验证数据完整性
        if not verify_data_integrity():
            print("数据完整性验证失败，请检查数据")
            sys.exit(1)
        
        print("\n" + "=" * 50)
        print("迁移成功完成！")
        print("可以安全地使用新的数据库系统")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n迁移过程中发生错误: {e}")
        print("请检查错误信息并重试")
        sys.exit(1)


if __name__ == "__main__":
    main() 