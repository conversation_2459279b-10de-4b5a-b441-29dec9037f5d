from flask import (
    Flask,
    render_template,
    request,
    jsonify,
    redirect,
    url_for,
    send_file,
    Response,
)
import os
import json
import requests
import PyPDF2
import hashlib
from difflib import SequenceMatcher
import re

from datetime import datetime
import xml.etree.ElementTree as ET
from rag_service import RAGService

app = Flask(__name__)
app.config["UPLOAD_FOLDER"] = "uploads"
app.config["MAX_CONTENT_LENGTH"] = 16 * 1024 * 1024  # 16MB max file size


# 加载配置
def load_config():
    """加载配置文件"""
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print("警告：config.json文件未找到，使用默认配置")
        return {
            "llm": {
                "provider": "openrouter",
                "base_url": "https://openrouter.ai/api/v1",
                "model": "google/gemini-2.5-flash-lite-preview-06-17",
                "max_tokens": 2048,
                "temperature": 0.7,
            }
        }
    except json.JSONDecodeError:
        print("错误：config.json格式不正确")
        return {}


# 全局配置
CONFIG = load_config()

# 初始化RAG服务
rag_service = RAGService(CONFIG)


def call_llm_api(messages, paper_context=None):
    """调用LLM API进行对话"""
    try:
        llm_config = CONFIG.get("llm", {})

        # 构建系统提示词
        system_prompt = """你是一个专业的学术论文阅读助手。你的任务是帮助用户理解和分析学术论文。

请遵循以下准则：
1. 提供准确、专业的学术分析
2. 用清晰易懂的语言解释复杂概念
3. 结合论文内容回答问题
4. 如果问题超出论文范围，请明确说明
5. 保持客观、中性的学术态度

当前论文信息："""

        if paper_context:
            system_prompt += f"""
标题：{paper_context.get('title', '未知')}
作者：{paper_context.get('authors', '未知')}
摘要：{paper_context.get('abstract', '无摘要')}
来源：{paper_context.get('source', '未知')}
"""

        # 构建API请求
        api_messages = [{"role": "system", "content": system_prompt}]
        api_messages.extend(messages)

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",  # OpenRouter要求
            "X-Title": "DocuMancer",  # OpenRouter要求
        }

        payload = {
            "model": llm_config.get(
                "model", "google/gemini-2.5-flash-lite-preview-06-17"
            ),
            "messages": api_messages,
            "max_tokens": llm_config.get("max_tokens", 2048),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": False,
        }

        print(f"正在调用LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30,
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_llm_api_stream(messages, paper_context=None):
    """调用LLM API进行流式对话"""
    try:
        llm_config = CONFIG.get("llm", {})

        # 构建系统提示词
        system_prompt = """你是一个专业的学术论文阅读助手。你的任务是帮助用户理解和分析学术论文。

请遵循以下准则：
1. 提供准确、专业的学术分析
2. 用清晰易懂的语言解释复杂概念
3. 结合论文内容回答问题
4. 如果问题超出论文范围，请明确说明
5. 保持客观、中性的学术态度

当前论文信息："""

        if paper_context:
            system_prompt += f"""
标题：{paper_context.get('title', '未知')}
作者：{paper_context.get('authors', '未知')}
摘要：{paper_context.get('abstract', '无摘要')}
来源：{paper_context.get('source', '未知')}
"""

        # 构建API请求
        api_messages = [{"role": "system", "content": system_prompt}]
        api_messages.extend(messages)

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",  # OpenRouter要求
            "X-Title": "DocuMancer",  # OpenRouter要求
        }

        payload = {
            "model": llm_config.get(
                "model", "google/gemini-2.5-flash-lite-preview-06-17"
            ),
            "messages": api_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": True,
        }

        print(f"正在调用流式LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,
            stream=True,
        )

        if response.status_code == 200:
            # 返回流式响应的生成器
            for line in response.iter_lines():
                if line:
                    line_str = line.decode("utf-8")
                    if line_str.startswith("data: "):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
        else:
            print(f"流式API调用失败: {response.status_code}, {response.text}")
            yield f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"流式LLM API调用错误: {e}")
        yield "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_llm_api_with_rag(messages, paper_id, paper_context=None):
    """调用LLM API进行RAG增强的对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            return call_llm_api(messages, paper_context)

        # 确保RAG上下文存在
        if not rag_service.get_or_create_rag_context(paper_id, paper_context):
            print(f"RAG上下文创建失败，回退到普通对话模式")
            return call_llm_api(messages, paper_context)

        # 搜索相关文档
        relevant_docs = rag_service.search_relevant_documents(
            paper_id, user_message, k=5
        )

        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            return call_llm_api(messages, paper_context)

        # 创建RAG增强的提示词
        rag_prompt = rag_service.create_rag_prompt(
            user_message, relevant_docs, paper_context
        )

        # 构建RAG对话消息
        rag_messages = []

        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get("role") and msg.get("content"):
                rag_messages.append(msg)

        # 添加RAG增强的用户消息
        rag_messages.append({"role": "user", "content": rag_prompt})

        # 调用LLM API
        llm_config = CONFIG.get("llm", {})

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "DocuMancer",
        }

        payload = {
            "model": llm_config.get("model", "google/gemini-2.5-flash"),
            "messages": rag_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": False,
        }

        print(f"正在调用RAG增强的LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30,
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"RAG API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"RAG API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"RAG LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_llm_api_stream_with_rag(messages, paper_id, paper_context=None):
    """调用LLM API进行RAG增强的流式对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            yield from call_llm_api_stream(messages, paper_context)
            return

        # 确保RAG上下文存在
        if not rag_service.get_or_create_rag_context(paper_id, paper_context):
            print(f"RAG上下文创建失败，回退到普通对话模式")
            yield from call_llm_api_stream(messages, paper_context)
            return

        # 搜索相关文档
        relevant_docs = rag_service.search_relevant_documents(
            paper_id, user_message, k=5
        )

        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            yield from call_llm_api_stream(messages, paper_context)
            return

        # 创建RAG增强的提示词
        rag_prompt = rag_service.create_rag_prompt(
            user_message, relevant_docs, paper_context
        )

        # 构建RAG对话消息
        rag_messages = []

        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get("role") and msg.get("content"):
                rag_messages.append(msg)

        # 添加RAG增强的用户消息
        rag_messages.append({"role": "user", "content": rag_prompt})

        # 调用流式LLM API
        llm_config = CONFIG.get("llm", {})

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "DocuMancer",
        }

        payload = {
            "model": llm_config.get("model", "google/gemini-2.5-flash"),
            "messages": rag_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": True,
        }

        print(f"正在调用RAG增强的流式LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,
            stream=True,
        )

        if response.status_code == 200:
            # 返回流式响应的生成器
            for line in response.iter_lines():
                if line:
                    line_str = line.decode("utf-8")
                    if line_str.startswith("data: "):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
        else:
            print(f"RAG流式API调用失败: {response.status_code}, {response.text}")
            yield f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"RAG流式LLM API调用错误: {e}")
        yield "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_llm_api_with_multi_doc_rag(
    messages, main_paper_id, reference_paper_ids, main_paper, reference_papers
):
    """调用LLM API进行多文档RAG增强的对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            return call_llm_api(messages, main_paper)

        # 确保所有论文的RAG上下文存在
        if not rag_service.get_or_create_rag_context(main_paper_id, main_paper):
            print(f"主论文RAG上下文创建失败，回退到普通对话模式")
            return call_llm_api(messages, main_paper)

        # 确保参考论文的RAG上下文存在
        for ref_id, ref_paper in zip(reference_paper_ids, reference_papers):
            if not rag_service.get_or_create_rag_context(ref_id, ref_paper):
                print(f"参考论文 {ref_id} RAG上下文创建失败")

        # 进行多文档搜索
        relevant_docs = rag_service.search_multi_doc_relevant_documents(
            main_paper_id,
            reference_paper_ids,
            user_message,
            k=8,  # 多文档模式下获取更多相关内容
        )

        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            return call_llm_api(messages, main_paper)

        # 创建多文档RAG增强的提示词
        rag_prompt = rag_service.create_multi_doc_rag_prompt(
            user_message, relevant_docs, main_paper, reference_papers
        )

        # 构建RAG对话消息
        rag_messages = []

        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get("role") and msg.get("content"):
                rag_messages.append(msg)

        # 添加RAG增强的用户消息
        rag_messages.append({"role": "user", "content": rag_prompt})

        # 调用LLM API
        llm_config = CONFIG.get("llm", {})

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "DocuMancer",
        }

        payload = {
            "model": llm_config.get("model", "google/gemini-2.5-flash"),
            "messages": rag_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": False,
        }

        print(f"正在调用多文档RAG增强的LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30,
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"多文档RAG API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"多文档RAG API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"多文档RAG LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_pre_read_api(pdf_content, prompt_content):
    """调用预阅读模型API"""
    try:
        pre_read_config = CONFIG.get("pre_read_model", {})

        # 构建系统提示词
        system_prompt = prompt_content

        # 构建用户消息，包含PDF内容
        user_message = f"请分析以下论文内容：\n\n{pdf_content}"

        # 构建API请求
        api_messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message},
        ]

        headers = {
            "Authorization": f'Bearer {pre_read_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",  # OpenRouter要求
            "X-Title": "DocuMancer",  # OpenRouter要求
        }

        payload = {
            "model": pre_read_config.get("model", "google/gemini-2.5-pro"),
            "messages": api_messages,
            "max_tokens": pre_read_config.get("max_tokens", 4096),
            "temperature": pre_read_config.get("temperature", 0.3),
            "stream": False,
        }

        print(f"正在调用预阅读API: {pre_read_config.get('model')}")

        response = requests.post(
            f"{pre_read_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,  # 预阅读可能需要更长时间
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"预阅读API响应格式错误: {result}")
                return "抱歉，预阅读服务暂时无法使用，请稍后再试。"
        else:
            print(f"预阅读API调用失败: {response.status_code}, {response.text}")
            return (
                f"预阅读服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"
            )

    except Exception as e:
        print(f"预阅读API调用错误: {e}")
        return "抱歉，预阅读服务遇到了技术问题，请稍后再试。"


# 确保上传目录存在
os.makedirs(app.config["UPLOAD_FOLDER"], exist_ok=True)
os.makedirs("data", exist_ok=True)
os.makedirs("pre_reads", exist_ok=True)  # 确保预阅读目录存在
os.makedirs("hidden_pre_reads", exist_ok=True)  # 确保隐藏预阅读目录存在
os.makedirs("vector_store", exist_ok=True)  # 确保向量数据库目录存在

# 论文数据存储（简单的JSON文件存储）
PAPERS_FILE = "data/papers.json"


def load_papers():
    """加载已保存的论文数据"""
    try:
        with open(PAPERS_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []


def save_papers(papers):
    """保存论文数据"""
    with open(PAPERS_FILE, "w", encoding="utf-8") as f:
        json.dump(papers, f, ensure_ascii=False, indent=2)


def extract_pdf_info(file_path):
    """从PDF文件提取论文信息"""
    title = ""
    authors = ""
    abstract = ""
    full_text = ""

    try:
        with open(file_path, "rb") as file:
            pdf_reader = PyPDF2.PdfReader(file)

            # 提取文本内容
            # 尝试读取前几页以获取标题、作者和摘要
            for page_num in range(min(len(pdf_reader.pages), 5)):  # 读取前5页
                full_text += pdf_reader.pages[page_num].extract_text() + "\n"

            # 1. 尝试从PDF元数据获取信息
            metadata = pdf_reader.metadata
            if metadata:
                title = metadata.get("/Title", "").strip()
                authors = metadata.get("/Author", "").strip()

            # 2. 如果元数据没有标题，尝试从文本提取
            if not title:
                lines = full_text.split("\n")
                # 寻找可能作为标题的行：通常较长，可能全大写或首字母大写
                for line in lines[:15]:  # 检查前15行
                    stripped_line = line.strip()
                    if 20 < len(stripped_line) < 200 and stripped_line.isupper():
                        title = stripped_line
                        break
                    elif 20 < len(stripped_line) < 200 and stripped_line[0].isupper() and stripped_line.count(' ') > 2:
                        # 尝试匹配看起来像标题的行（首字母大写，有多个单词）
                        title = stripped_line
                        break
                
                # 进一步尝试，寻找常见的标题模式
                title_patterns = [
                    r"^\s*Title[:\s]*([^\n]+)",
                    r"^\s*Paper Title[:\s]*([^\n]+)",
                ]
                for pattern in title_patterns:
                    match = re.search(pattern, full_text, re.IGNORECASE | re.MULTILINE)
                    if match:
                        title = match.group(1).strip()
                        break

            # 3. 尝试从文本提取作者
            if not authors:
                # 寻找常见的作者模式，通常在标题下方
                author_patterns = [
                    r"(?i)(?:Author(?:s)?|By)[:\s]*([^\n]+)", # "Author(s): Name"
                    r"[\n]([A-Z][a-zA-Z\s\.\-]+(?:and|,\s*|\s+)+[A-Z][a-zA-Z\s\.\-]+)", # "First Last and Second Last"
                ]
                for pattern in author_patterns:
                    match = re.search(pattern, full_text, re.IGNORECASE)
                    if match:
                        authors = match.group(1).strip()
                        # 进一步清理，移除邮箱、机构等
                        authors = re.sub(r"\S*@\S*", "", authors) # 移除邮箱
                        authors = re.sub(r"\(.*?\)", "", authors) # 移除括号内容
                        authors = re.sub(r"\d+", "", authors) # 移除数字（脚注）
                        authors = authors.replace("\n", ", ").strip()
                        break
            
            # 4. 摘要提取（查找Abstract关键词）
            abstract_match = re.search(
                r"(?i)abstract[:\s\n]+(.*?)(?=\n\s*(?:keywords?|introduction|1\.|references?|\n\n[A-Z]))",
                full_text,
                re.DOTALL,
            )
            if abstract_match:
                abstract = abstract_match.group(1).strip()[:1000]  # 限制长度，稍微放宽

            # 5. 如果标题仍然为空，使用文件名作为备用
            if not title:
                title = os.path.splitext(os.path.basename(file_path))[0]
                # 尝试清理文件名中的日期和随机字符串
                title = re.sub(r"^\d{8}_\d{6}_", "", title) # 移除日期时间前缀
                title = re.sub(r"_\d{10,}", "", title) # 移除长数字后缀
                title = title.replace("_", " ").strip()


            return {
                "title": title or "Unknown Title",
                "authors": authors or "Unknown Author",
                "abstract": abstract or "No abstract available",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "source": "PDF Upload",
                "has_pdf": True,
            }
    except Exception as e:
        print(f"PDF解析错误: {e}")
        return {
            "title": os.path.splitext(os.path.basename(file_path))[0] or "PDF Parse Error",
            "authors": "Unknown",
            "abstract": "Failed to extract information from PDF",
            "date": datetime.now().strftime("%Y-%m-%d"),
            "source": "PDF Upload",
            "has_pdf": False,
        }


def download_arxiv_pdf(arxiv_id):
    """从arXiv下载PDF文件"""
    try:
        # 清理arXiv ID
        clean_id = arxiv_id.replace("https://arxiv.org/abs/", "").replace(
            "http://arxiv.org/abs/", ""
        )

        # arXiv PDF URL
        pdf_url = f"http://arxiv.org/pdf/{clean_id}.pdf"

        # 生成本地文件名
        filename = f"arxiv_{clean_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        file_path = os.path.join(app.config["UPLOAD_FOLDER"], filename)

        # 下载PDF文件
        print(f"正在下载arXiv PDF: {pdf_url}")
        response = requests.get(pdf_url, timeout=30, stream=True)

        if response.status_code == 200:
            # 检查响应内容类型
            content_type = response.headers.get("content-type", "")
            if (
                "application/pdf" in content_type
                or "application/octet-stream" in content_type
            ):
                # 保存文件
                with open(file_path, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                print(f"arXiv PDF下载成功: {file_path}")
                return file_path
            else:
                print(f"下载的文件不是PDF格式: {content_type}")
                return None
        else:
            print(f"arXiv PDF下载失败: HTTP {response.status_code}")
            return None

    except Exception as e:
        print(f"arXiv PDF下载错误: {e}")
        return None


def fetch_arxiv_paper(arxiv_id):
    """从arXiv获取论文信息并下载PDF"""
    try:
        # 清理arXiv ID
        arxiv_id = arxiv_id.replace("https://arxiv.org/abs/", "").replace(
            "http://arxiv.org/abs/", ""
        )

        # 调用arXiv API获取论文信息
        url = f"http://export.arxiv.org/api/query?id_list={arxiv_id}"
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            # 解析XML响应
            root = ET.fromstring(response.content)
            entry = root.find("{http://www.w3.org/2005/Atom}entry")

            if entry is not None:
                title = entry.find("{http://www.w3.org/2005/Atom}title").text.strip()

                # 获取作者
                authors = []
                for author in entry.findall("{http://www.w3.org/2005/Atom}author"):
                    name = author.find("{http://www.w3.org/2005/Atom}name").text
                    authors.append(name)

                # 获取摘要
                abstract = entry.find(
                    "{http://www.w3.org/2005/Atom}summary"
                ).text.strip()

                # 获取发布日期
                published = entry.find("{http://www.w3.org/2005/Atom}published").text
                date = published.split("T")[0]  # 提取日期部分

                # 下载PDF文件
                pdf_path = download_arxiv_pdf(arxiv_id)

                paper_info = {
                    "title": title,
                    "authors": ", ".join(authors),
                    "abstract": abstract,
                    "date": date,
                    "source": f"arXiv:{arxiv_id}",
                    "arxiv_id": arxiv_id,
                }

                # 如果PDF下载成功，添加文件路径
                if pdf_path:
                    paper_info["file_path"] = pdf_path
                    paper_info["has_pdf"] = True
                else:
                    paper_info["has_pdf"] = False
                    print(f"警告：arXiv论文 {arxiv_id} 的PDF下载失败，但论文信息已获取")

                return paper_info

    except Exception as e:
        print(f"arXiv获取错误: {e}")
        return None


def calculate_similarity(str1, str2):
    """计算两个字符串的相似度"""
    if not str1 or not str2:
        return 0.0
    return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()


def normalize_title(title):
    """标准化标题，移除特殊字符和多余空格"""
    if not title:
        return ""
    # 移除特殊字符，保留字母、数字和空格
    normalized = re.sub(r"[^\w\s]", " ", title)
    # 移除多余空格
    normalized = re.sub(r"\s+", " ", normalized).strip()
    return normalized.lower()


def normalize_authors(authors):
    """标准化作者信息"""
    if not authors:
        return ""
    # 移除多余空格和标点
    normalized = re.sub(r"[^\w\s,]", " ", authors)
    normalized = re.sub(r"\s+", " ", normalized).strip()
    return normalized.lower()


def is_duplicate_paper(new_paper, existing_papers, similarity_threshold=0.8):
    """
    检查新论文是否与现有论文重复

    Args:
        new_paper: 新论文信息
        existing_papers: 现有论文列表
        similarity_threshold: 相似度阈值

    Returns:
        tuple: (是否重复, 重复的论文信息)
    """
    new_title = normalize_title(new_paper.get("title", ""))
    new_authors = normalize_authors(new_paper.get("authors", ""))
    new_arxiv_id = new_paper.get("arxiv_id", "").strip()

    for existing_paper in existing_papers:
        existing_title = normalize_title(existing_paper.get("title", ""))
        existing_authors = normalize_authors(existing_paper.get("authors", ""))
        existing_arxiv_id = existing_paper.get("arxiv_id", "").strip()

        # 1. arXiv ID 精确匹配
        if new_arxiv_id and existing_arxiv_id and new_arxiv_id == existing_arxiv_id:
            return True, existing_paper

        # 2. 标题相似度检测
        title_similarity = calculate_similarity(new_title, existing_title)
        if title_similarity >= similarity_threshold:
            # 如果标题非常相似，进一步检查作者
            authors_similarity = calculate_similarity(new_authors, existing_authors)
            if authors_similarity >= 0.7:  # 作者相似度阈值
                return True, existing_paper

        # 3. 文件内容哈希匹配（如果有PDF文件）
        if (
            new_paper.get("file_path")
            and existing_paper.get("file_path")
            and os.path.exists(new_paper["file_path"])
            and os.path.exists(existing_paper["file_path"])
        ):
            try:
                with open(new_paper["file_path"], "rb") as f1, open(
                    existing_paper["file_path"], "rb"
                ) as f2:
                    if f1.read() == f2.read():
                        return True, existing_paper
            except Exception as e:
                print(f"文件比较错误: {e}")

    return False, None


def remove_duplicate_papers(papers):
    """
    移除重复的论文。
    使用智能去重逻辑，包括标题相似度、作者匹配和文件哈希。
    """
    if not papers:
        return []

    unique_papers = [papers[0]]  # 保留第一篇论文

    for i in range(1, len(papers)):
        current_paper = papers[i]
        is_duplicate = False

        # 检查是否与已保留的论文重复
        for existing_paper in unique_papers:
            duplicate, _ = is_duplicate_paper(current_paper, [existing_paper])
            if duplicate:
                is_duplicate = True
                print(f"移除重复论文: {current_paper.get('title', '未知')}")
                break

        if not is_duplicate:
            unique_papers.append(current_paper)

    return unique_papers


@app.route("/")
def index():
    """论文库主页"""
    papers = load_papers()
    return render_template("index.html", papers=papers)


@app.route("/upload_pdf", methods=["POST"])
def upload_pdf():
    """处理PDF上传"""
    if "pdf_file" not in request.files:
        return jsonify({"error": "No file uploaded"}), 400

    file = request.files["pdf_file"]
    if file.filename == "":
        return jsonify({"error": "No file selected"}), 400

    if file and file.filename.lower().endswith(".pdf"):
        # 保存文件
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
        file_path = os.path.join(app.config["UPLOAD_FOLDER"], filename)
        file.save(file_path)

        # 解析PDF信息
        paper_info = extract_pdf_info(file_path)
        paper_info["id"] = len(load_papers()) + 1
        paper_info["file_path"] = file_path

        # 检查是否重复
        papers = load_papers()
        is_duplicate, duplicate_paper = is_duplicate_paper(paper_info, papers)

        if is_duplicate:
            # 删除新上传的文件
            try:
                os.remove(file_path)
                print(f"检测到重复论文，已删除新上传的文件: {file_path}")
            except Exception as e:
                print(f"删除重复文件失败: {e}")

            return (
                jsonify(
                    {
                        "success": False,
                        "error": "检测到重复论文",
                        "duplicate_paper": {
                            "id": duplicate_paper["id"],
                            "title": duplicate_paper["title"],
                            "authors": duplicate_paper["authors"],
                            "source": duplicate_paper["source"],
                        },
                        "message": f'论文 "{duplicate_paper["title"]}" 已存在于论文库中',
                    }
                ),
                409,
            )  # 409 Conflict

        # 保存到数据库
        papers.append(paper_info)
        save_papers(papers)

        return jsonify({"success": True, "paper": paper_info})

    return jsonify({"error": "Invalid file type"}), 400


@app.route("/add_arxiv", methods=["POST"])
def add_arxiv():
    """添加arXiv论文"""
    data = request.get_json()
    arxiv_id = data.get("arxiv_id", "").strip()

    if not arxiv_id:
        return jsonify({"error": "ArXiv ID required"}), 400

    paper_info = fetch_arxiv_paper(arxiv_id)
    if paper_info:
        paper_info["id"] = len(load_papers()) + 1

        # 检查是否重复
        papers = load_papers()
        is_duplicate, duplicate_paper = is_duplicate_paper(paper_info, papers)

        if is_duplicate:
            # 删除新下载的文件（如果存在）
            if paper_info.get("file_path") and os.path.exists(paper_info["file_path"]):
                try:
                    os.remove(paper_info["file_path"])
                    print(
                        f"检测到重复论文，已删除新下载的文件: {paper_info['file_path']}"
                    )
                except Exception as e:
                    print(f"删除重复文件失败: {e}")

            return (
                jsonify(
                    {
                        "success": False,
                        "error": "检测到重复论文",
                        "duplicate_paper": {
                            "id": duplicate_paper["id"],
                            "title": duplicate_paper["title"],
                            "authors": duplicate_paper["authors"],
                            "source": duplicate_paper["source"],
                        },
                        "message": f'论文 "{duplicate_paper["title"]}" 已存在于论文库中',
                    }
                ),
                409,
            )  # 409 Conflict

        # 保存到数据库
        papers.append(paper_info)
        save_papers(papers)

        return jsonify({"success": True, "paper": paper_info})
    else:
        return jsonify({"error": "Failed to fetch arXiv paper"}), 400


@app.route("/delete_paper/<int:paper_id>", methods=["DELETE"])
def delete_paper(paper_id):
    """删除论文"""
    try:
        # 加载论文数据
        papers = load_papers()

        # 查找要删除的论文
        paper_to_delete = None
        papers_updated = []

        for paper in papers:
            if paper["id"] == paper_id:
                paper_to_delete = paper
            else:
                papers_updated.append(paper)

        if not paper_to_delete:
            return jsonify({"error": "论文不存在"}), 404

        # 删除PDF文件（如果存在）
        if paper_to_delete.get("file_path") and os.path.exists(
            paper_to_delete["file_path"]
        ):
            try:
                os.remove(paper_to_delete["file_path"])
                print(f"已删除PDF文件: {paper_to_delete['file_path']}")
            except Exception as e:
                print(f"删除PDF文件失败: {e}")
                # 即使文件删除失败，也继续删除数据库记录

        # 保存更新后的论文列表
        save_papers(papers_updated)

        return jsonify(
            {
                "success": True,
                "message": "论文删除成功",
                "deleted_paper": {
                    "id": paper_to_delete["id"],
                    "title": paper_to_delete["title"],
                },
            }
        )

    except Exception as e:
        print(f"删除论文错误: {e}")
        return (
            jsonify({"error": "删除论文时发生错误，请稍后重试", "success": False}),
            500,
        )


@app.route("/chat/<int:paper_id>")
def chat_with_paper(paper_id):
    """与特定论文聊天"""
    papers = load_papers()
    paper = next((p for p in papers if p["id"] == paper_id), None)

    if not paper:
        return redirect(url_for("index"))

    return render_template("chat.html", paper=paper)


@app.route("/view_pdf/<int:paper_id>")
def view_pdf(paper_id):
    """查看论文PDF文件"""
    papers = load_papers()
    paper = next((p for p in papers if p["id"] == paper_id), None)

    if not paper or not paper.get("file_path"):
        return "PDF文件不存在", 404

    try:
        return send_file(paper["file_path"], mimetype="application/pdf")
    except Exception as e:
        print(f"PDF文件读取错误: {e}")
        return "PDF文件读取失败", 500


@app.route("/chat_api", methods=["POST"])
def chat_api():
    """聊天API - 与论文进行智能对话"""
    data = request.get_json()
    message = data.get("message", "").strip()
    paper_id = data.get("paper_id")
    chat_history = data.get("chat_history", [])  # 获取对话历史

    if not message:
        return jsonify({"error": "消息不能为空"}), 400

    if not paper_id:
        return jsonify({"error": "缺少论文ID"}), 400

    try:
        # 获取论文信息
        papers = load_papers()
        paper = next((p for p in papers if p["id"] == paper_id), None)

        if not paper:
            return jsonify({"error": "论文不存在"}), 404

        # 构建对话消息列表
        messages = []

        # 添加历史对话
        for history_item in chat_history[-10:]:  # 只保留最近10轮对话
            if history_item.get("role") and history_item.get("content"):
                messages.append(
                    {"role": history_item["role"], "content": history_item["content"]}
                )

        # 添加当前用户消息
        messages.append({"role": "user", "content": message})

        # 调用RAG增强的LLM API
        response_text = call_llm_api_with_rag(messages, paper_id, paper_context=paper)

        return jsonify(
            {
                "success": True,
                "response": response_text,
                "paper_title": paper.get("title", ""),
                "timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        print(f"聊天API错误: {e}")
        return jsonify({"error": "服务器内部错误，请稍后重试", "success": False}), 500


@app.route("/chat_stream", methods=["POST"])
def chat_stream():
    """流式聊天API - 与论文进行智能对话"""
    data = request.get_json()
    message = data.get("message", "").strip()
    paper_id = data.get("paper_id")
    chat_history = data.get("chat_history", [])

    if not message:
        return jsonify({"error": "消息不能为空"}), 400

    if not paper_id:
        return jsonify({"error": "缺少论文ID"}), 400

    try:
        # 获取论文信息
        papers = load_papers()
        paper = next((p for p in papers if p["id"] == paper_id), None)

        if not paper:
            return jsonify({"error": "论文不存在"}), 404

        # 构建对话消息列表
        messages = []

        # 添加历史对话
        for history_item in chat_history[-10:]:
            if history_item.get("role") and history_item.get("content"):
                messages.append(
                    {"role": history_item["role"], "content": history_item["content"]}
                )

        # 添加当前用户消息
        messages.append({"role": "user", "content": message})

        def generate():
            try:
                for chunk in call_llm_api_stream_with_rag(
                    messages, paper_id, paper_context=paper
                ):
                    # 发送每个数据块
                    yield f"data: {json.dumps({'content': chunk, 'type': 'content'})}\n\n"

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

            except Exception as e:
                print(f"流式聊天错误: {e}")
                yield f"data: {json.dumps({'content': '抱歉，出现了错误，请稍后重试。', 'type': 'error'})}\n\n"
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

        return Response(
            generate(),
            mimetype="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        print(f"流式聊天API错误: {e}")
        return jsonify({"error": "服务器内部错误，请稍后重试", "success": False}), 500


@app.route("/generate_pre_read/<int:paper_id>", methods=["POST"])
def generate_pre_read(paper_id):
    """生成论文预阅读"""
    try:
        # 获取论文信息
        papers = load_papers()
        paper = next((p for p in papers if p["id"] == paper_id), None)

        if not paper:
            return jsonify({"error": "论文不存在"}), 404

        # 检查是否有PDF文件
        if not paper.get("file_path") or not os.path.exists(paper["file_path"]):
            return jsonify({"error": "论文PDF文件不存在"}), 404

        # 检查是否已经有预阅读文件
        pre_read_filename = f"pre_read_{paper_id}.md"
        pre_read_path = os.path.join("pre_reads", pre_read_filename)

        if os.path.exists(pre_read_path):
            return jsonify({"success": True, "message": "预阅读已存在", "exists": True})

        # 读取PDF内容
        pdf_content = ""
        try:
            with open(paper["file_path"], "rb") as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    pdf_content += page.extract_text()
        except Exception as e:
            print(f"PDF读取错误: {e}")
            return jsonify({"error": "PDF文件读取失败"}), 500

        # 读取预阅读提示词
        try:
            with open("static/pre_reads_prompt.md", "r", encoding="utf-8") as f:
                prompt_content = f.read()
        except Exception as e:
            print(f"提示词文件读取错误: {e}")
            return jsonify({"error": "预阅读提示词文件读取失败"}), 500

        # 调用预阅读API
        pre_read_result = call_pre_read_api(pdf_content, prompt_content)

        # 保存预阅读结果
        try:
            with open(pre_read_path, "w", encoding="utf-8") as f:
                f.write(pre_read_result)
        except Exception as e:
            print(f"预阅读文件保存错误: {e}")
            return jsonify({"error": "预阅读结果保存失败"}), 500

        # 删除隐藏预阅读（如果存在）
        rag_service.delete_hidden_pre_read(paper_id)

        # 重新创建向量数据库（使用正式预阅读）
        rag_service.create_vector_store_from_pre_read(paper_id, pre_read_result)

        return jsonify(
            {
                "success": True,
                "message": "预阅读生成成功",
                "pre_read_path": pre_read_filename,
            }
        )

    except Exception as e:
        print(f"预阅读生成错误: {e}")
        return (
            jsonify({"error": "预阅读生成时发生错误，请稍后重试", "success": False}),
            500,
        )


@app.route("/view_pre_read/<int:paper_id>")
def view_pre_read(paper_id):
    """查看论文预阅读"""
    try:
        # 获取论文信息
        papers = load_papers()
        paper = next((p for p in papers if p["id"] == paper_id), None)

        if not paper:
            return redirect(url_for("index"))

        # 检查预阅读文件是否存在
        pre_read_filename = f"pre_read_{paper_id}.md"
        pre_read_path = os.path.join("pre_reads", pre_read_filename)

        pre_read_content = ""
        pre_read_exists = False

        if os.path.exists(pre_read_path):
            try:
                with open(pre_read_path, "r", encoding="utf-8") as f:
                    pre_read_content = f.read()
                pre_read_exists = True
            except Exception as e:
                print(f"预阅读文件读取错误: {e}")
                pre_read_content = "预阅读文件读取失败"

        return render_template(
            "pre_read.html",
            paper=paper,
            pre_read_content=pre_read_content,
            pre_read_exists=pre_read_exists,
        )

    except Exception as e:
        print(f"预阅读查看错误: {e}")
        return redirect(url_for("index"))


@app.route("/api/papers", methods=["GET"])
def api_get_papers():
    """获取论文列表API"""
    try:
        papers = load_papers()

        # 为每篇论文添加预阅读状态信息
        for paper in papers:
            # 检查是否有正式预阅读
            formal_pre_read_path = os.path.join(
                "pre_reads", f"pre_read_{paper['id']}.md"
            )
            paper["has_formal_preread"] = os.path.exists(formal_pre_read_path)

            # 检查是否有隐藏预阅读
            hidden_pre_read_path = os.path.join(
                "hidden_pre_reads", f"hidden_pre_read_{paper['id']}.md"
            )
            paper["has_hidden_preread"] = os.path.exists(hidden_pre_read_path)

        return jsonify({"success": True, "papers": papers})

    except Exception as e:
        print(f"获取论文列表错误: {e}")
        return jsonify({"error": "获取论文列表失败"}), 500


@app.route("/api/start_multi_doc_analysis", methods=["POST"])
def api_start_multi_doc_analysis():
    """初始化跨文档分析API"""
    try:
        data = request.get_json()
        main_paper_id = data.get("main_paper_id")
        reference_paper_ids = data.get("reference_paper_ids", [])

        if not main_paper_id:
            return jsonify({"error": "缺少主论文ID"}), 400

        if not reference_paper_ids:
            return jsonify({"error": "请至少选择一篇参考论文"}), 400

        # 验证论文是否存在
        papers = load_papers()
        main_paper = next((p for p in papers if p["id"] == main_paper_id), None)
        if not main_paper:
            return jsonify({"error": "主论文不存在"}), 404

        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = next((p for p in papers if p["id"] == ref_id), None)
            if ref_paper:
                reference_papers.append(ref_paper)
            else:
                return jsonify({"error": f"参考论文ID {ref_id} 不存在"}), 404

        # 确保所有参考论文都有预阅读（隐藏或正式）
        processed_papers = []
        for ref_paper in reference_papers:
            # 调用RAG服务确保预阅读存在
            if rag_service.get_or_create_rag_context(ref_paper["id"], ref_paper):
                processed_papers.append(ref_paper["id"])
            else:
                return (
                    jsonify({"error": f'论文 {ref_paper["title"]} 的预阅读生成失败'}),
                    500,
                )

        # 同样确保主论文有预阅读
        if not rag_service.get_or_create_rag_context(main_paper_id, main_paper):
            return jsonify({"error": "主论文的预阅读生成失败"}), 500

        return jsonify(
            {
                "success": True,
                "main_paper_id": main_paper_id,
                "reference_paper_ids": processed_papers,
                "message": f"跨文档分析初始化成功，包含 {len(processed_papers) + 1} 篇论文",
            }
        )

    except Exception as e:
        print(f"跨文档分析初始化错误: {e}")
        return jsonify({"error": "初始化失败，请稍后重试"}), 500


@app.route("/chat_multi_doc_api", methods=["POST"])
def chat_multi_doc_api():
    """跨文档分析聊天API"""
    data = request.get_json()
    message = data.get("message", "").strip()
    main_paper_id = data.get("main_paper_id")
    reference_paper_ids = data.get("reference_paper_ids", [])
    chat_history = data.get("chat_history", [])

    if not message:
        return jsonify({"error": "消息不能为空"}), 400

    if not main_paper_id:
        return jsonify({"error": "缺少主论文ID"}), 400

    try:
        # 获取论文信息
        papers = load_papers()
        main_paper = next((p for p in papers if p["id"] == main_paper_id), None)

        if not main_paper:
            return jsonify({"error": "主论文不存在"}), 404

        # 获取参考论文信息
        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = next((p for p in papers if p["id"] == ref_id), None)
            if ref_paper:
                reference_papers.append(ref_paper)

        # 构建对话消息列表
        messages = []

        # 添加历史对话
        for history_item in chat_history[-10:]:
            if history_item.get("role") and history_item.get("content"):
                messages.append(
                    {"role": history_item["role"], "content": history_item["content"]}
                )

        # 添加当前用户消息
        messages.append({"role": "user", "content": message})

        # 调用多文档RAG增强的LLM API
        response_text = call_llm_api_with_multi_doc_rag(
            messages, main_paper_id, reference_paper_ids, main_paper, reference_papers
        )

        return jsonify(
            {
                "success": True,
                "response": response_text,
                "main_paper_title": main_paper.get("title", ""),
                "reference_papers_count": len(reference_papers),
                "timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        print(f"跨文档聊天API错误: {e}")
        return jsonify({"error": "服务器内部错误，请稍后重试", "success": False}), 500


def call_llm_api_stream_with_multi_doc_rag(
    messages, main_paper_id, reference_paper_ids, main_paper, reference_papers
):
    """调用LLM API进行多文档RAG增强的流式对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return

        # 确保所有论文的RAG上下文存在
        if not rag_service.get_or_create_rag_context(main_paper_id, main_paper):
            print(f"主论文RAG上下文创建失败，回退到普通对话模式")
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return

        # 确保参考论文的RAG上下文存在
        for ref_id, ref_paper in zip(reference_paper_ids, reference_papers):
            if not rag_service.get_or_create_rag_context(ref_id, ref_paper):
                print(f"参考论文 {ref_id} RAG上下文创建失败")

        # 进行多文档搜索
        relevant_docs = rag_service.search_multi_doc_relevant_documents(
            main_paper_id,
            reference_paper_ids,
            user_message,
            k=8,  # 多文档模式下获取更多相关内容
        )

        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return

        # 创建多文档RAG增强的提示词
        rag_prompt = rag_service.create_multi_doc_rag_prompt(
            user_message, relevant_docs, main_paper, reference_papers
        )

        # 构建RAG对话消息
        rag_messages = []

        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get("role") and msg.get("content"):
                rag_messages.append(msg)

        # 添加RAG增强的用户消息
        rag_messages.append({"role": "user", "content": rag_prompt})

        # 调用流式LLM API
        llm_config = CONFIG.get("llm", {})

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "DocuMancer",
        }

        payload = {
            "model": llm_config.get("model", "google/gemini-2.5-flash"),
            "messages": rag_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": True,
        }

        print(f"正在调用多文档RAG流式LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,
            stream=True,
        )

        if response.status_code == 200:
            # 返回流式响应的生成器
            for line in response.iter_lines():
                if line:
                    line_str = line.decode("utf-8")
                    if line_str.startswith("data: "):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
        else:
            print(f"多文档RAG流式API调用失败: {response.status_code}, {response.text}")
            yield f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"多文档RAG流式LLM API调用错误: {e}")
        yield "抱歉，AI助手遇到了技术问题，请稍后再试。"


@app.route("/chat_multi_doc_stream", methods=["POST"])
def chat_multi_doc_stream():
    """跨文档分析流式聊天API"""
    data = request.get_json()
    message = data.get("message", "").strip()
    main_paper_id = data.get("main_paper_id")
    reference_paper_ids = data.get("reference_paper_ids", [])
    chat_history = data.get("chat_history", [])

    if not message:
        return jsonify({"error": "消息不能为空"}), 400

    if not main_paper_id:
        return jsonify({"error": "缺少主论文ID"}), 400

    try:
        # 获取论文信息
        papers = load_papers()
        main_paper = next((p for p in papers if p["id"] == main_paper_id), None)

        if not main_paper:
            return jsonify({"error": "主论文不存在"}), 404

        # 获取参考论文信息
        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = next((p for p in papers if p["id"] == ref_id), None)
            if ref_paper:
                reference_papers.append(ref_paper)

        # 构建对话消息列表
        messages = []

        # 添加历史对话
        for history_item in chat_history[-10:]:
            if history_item.get("role") and history_item.get("content"):
                messages.append(
                    {"role": history_item["role"], "content": history_item["content"]}
                )

        # 添加当前用户消息
        messages.append({"role": "user", "content": message})

        def generate():
            try:
                for chunk in call_llm_api_stream_with_multi_doc_rag(
                    messages,
                    main_paper_id,
                    reference_paper_ids,
                    main_paper,
                    reference_papers,
                ):
                    # 发送每个数据块
                    yield f"data: {json.dumps({'content': chunk, 'type': 'content'})}\n\n"

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

            except Exception as e:
                print(f"跨文档流式聊天错误: {e}")
                yield f"data: {json.dumps({'content': '抱歉，出现了错误，请稍后重试。', 'type': 'error'})}\n\n"
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

        return Response(
            generate(),
            mimetype="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        print(f"跨文档流式聊天API错误: {e}")
        return jsonify({"error": "服务器内部错误，请稍后重试", "success": False}), 500


@app.route("/api/switch_current_paper", methods=["POST"])
def api_switch_current_paper():
    """切换当前论文API"""
    try:
        data = request.get_json()
        new_current_paper_id = data.get("paper_id")

        if not new_current_paper_id:
            return jsonify({"error": "缺少论文ID"}), 400

        # 验证论文是否存在
        papers = load_papers()
        target_paper = next(
            (p for p in papers if p["id"] == new_current_paper_id), None
        )
        if not target_paper:
            return jsonify({"error": "论文不存在"}), 404

        # 确保论文有预阅读
        if not rag_service.get_or_create_rag_context(
            new_current_paper_id, target_paper
        ):
            return jsonify({"error": "论文的预阅读生成失败"}), 500

        return jsonify(
            {
                "success": True,
                "current_paper_id": new_current_paper_id,
                "paper_info": {
                    "id": target_paper["id"],
                    "title": target_paper["title"],
                    "authors": target_paper["authors"],
                    "source": target_paper["source"],
                    "has_pdf": target_paper.get("has_pdf", True),
                },
                "message": f'已切换到论文：{target_paper["title"]}',
            }
        )

    except Exception as e:
        print(f"切换当前论文错误: {e}")
        return jsonify({"error": "切换失败，请稍后重试"}), 500


@app.route("/api/clean_duplicates", methods=["POST"])
def api_clean_duplicates():
    """清理现有论文库中的重复论文"""
    try:
        papers = load_papers()
        original_count = len(papers)

        # 使用文件哈希去重
        cleaned_papers = remove_duplicate_papers(papers)
        cleaned_count = len(cleaned_papers)

        if cleaned_count < original_count:
            # 保存清理后的论文列表
            save_papers(cleaned_papers)

            return jsonify(
                {
                    "success": True,
                    "message": f"成功清理重复论文，从 {original_count} 篇减少到 {cleaned_count} 篇",
                    "removed_count": original_count - cleaned_count,
                    "remaining_count": cleaned_count,
                }
            )
        else:
            return jsonify(
                {
                    "success": True,
                    "message": "未发现重复论文",
                    "removed_count": 0,
                    "remaining_count": original_count,
                }
            )

    except Exception as e:
        print(f"清理重复论文错误: {e}")
        return jsonify({"error": "清理重复论文时发生错误", "success": False}), 500


if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=5000)
