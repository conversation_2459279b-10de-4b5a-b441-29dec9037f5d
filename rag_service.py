import os
import json
import hashlib
from typing import List, Dict, Optional
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain.docstore.document import Document
import requests
import PyPDF2


class RAGService:
    """RAG服务类，处理文档向量化和检索"""

    def __init__(self, config: Dict):
        self.config = config
        self.embeddings = HuggingFaceEmbeddings(
            model_name="sentence-transformers/all-MiniLM-L6-v2",
            model_kwargs={"device": "cpu"},
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )

        # 向量数据库存储目录
        self.vector_db_dir = "vector_store"
        os.makedirs(self.vector_db_dir, exist_ok=True)

    def get_paper_vector_store_path(self, paper_id: int) -> str:
        """获取论文向量数据库路径"""
        return os.path.join(self.vector_db_dir, f"paper_{paper_id}")

    def create_vector_store_from_pre_read(
        self, paper_id: int, pre_read_content: str
    ) -> bool:
        """从预阅读内容创建向量数据库"""
        try:
            # 分割文档
            documents = self.text_splitter.split_text(pre_read_content)
            docs = [
                Document(page_content=doc, metadata={"source": f"pre_read_{paper_id}"})
                for doc in documents
            ]

            # 创建向量数据库
            vector_store_path = self.get_paper_vector_store_path(paper_id)
            vectorstore = Chroma.from_documents(
                documents=docs,
                embedding=self.embeddings,
                persist_directory=vector_store_path,
            )
            vectorstore.persist()

            print(f"为论文 {paper_id} 创建向量数据库成功，共 {len(docs)} 个文档片段")
            return True

        except Exception as e:
            print(f"创建向量数据库失败: {e}")
            return False

    def load_vector_store(self, paper_id: int) -> Optional[Chroma]:
        """加载已存在的向量数据库"""
        try:
            vector_store_path = self.get_paper_vector_store_path(paper_id)
            if not os.path.exists(vector_store_path):
                return None

            vectorstore = Chroma(
                persist_directory=vector_store_path, embedding_function=self.embeddings
            )
            return vectorstore

        except Exception as e:
            print(f"加载向量数据库失败: {e}")
            return None

    def search_relevant_documents(
        self, paper_id: int, query: str, k: int = 5
    ) -> List[str]:
        """搜索相关文档片段"""
        try:
            vectorstore = self.load_vector_store(paper_id)
            if not vectorstore:
                return []

            # 搜索相关文档
            results = vectorstore.similarity_search(query, k=k)
            return [doc.page_content for doc in results]

        except Exception as e:
            print(f"搜索相关文档失败: {e}")
            return []

    def generate_hidden_pre_read(
        self, paper_id: int, pdf_content: str, prompt_content: str
    ) -> Optional[str]:
        """使用配置的模型生成隐藏的预阅读"""
        try:
            # 使用专门的隐藏预阅读模型配置
            hidden_pre_read_config = self.config.get("hidden_pre_read_model", {})

            # 构建API请求
            api_messages = [
                {"role": "system", "content": prompt_content},
                {"role": "user", "content": f"请分析以下论文内容：\n\n{pdf_content}"},
            ]

            headers = {
                "Authorization": f'Bearer {hidden_pre_read_config.get("api_key", "")}',
                "Content-Type": "application/json",
                "HTTP-Referer": "http://localhost:5000",
                "X-Title": "DocuMancer",
            }

            payload = {
                "model": hidden_pre_read_config.get(
                    "model", "google/gemini-2.5-flash-lite-preview-06-17"
                ),
                "messages": api_messages,
                "max_tokens": hidden_pre_read_config.get("max_tokens", 131072),
                "temperature": hidden_pre_read_config.get("temperature", 0.3),
                "stream": False,
            }

            print(
                f"正在使用{hidden_pre_read_config.get('model', 'default')}生成隐藏预阅读..."
            )

            response = requests.post(
                f"{hidden_pre_read_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60,
            )

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    hidden_pre_read = result["choices"][0]["message"]["content"]

                    # 保存隐藏预阅读到特殊目录
                    hidden_pre_read_dir = "hidden_pre_reads"
                    os.makedirs(hidden_pre_read_dir, exist_ok=True)
                    hidden_pre_read_path = os.path.join(
                        hidden_pre_read_dir, f"hidden_pre_read_{paper_id}.md"
                    )

                    with open(hidden_pre_read_path, "w", encoding="utf-8") as f:
                        f.write(hidden_pre_read)

                    print(f"隐藏预阅读生成成功: {hidden_pre_read_path}")
                    return hidden_pre_read
                else:
                    print(f"隐藏预阅读API响应格式错误: {result}")
                    return None
            else:
                print(f"隐藏预阅读API调用失败: {response.status_code}, {response.text}")
                return None

        except Exception as e:
            print(f"生成隐藏预阅读错误: {e}")
            return None

    def get_or_create_rag_context(self, paper_id: int, paper_info: Dict) -> bool:
        """获取或创建RAG上下文"""
        try:
            # 1. 检查是否已有向量数据库
            if self.load_vector_store(paper_id):
                print(f"论文 {paper_id} 的向量数据库已存在")
                return True

            # 2. 检查是否有现有的预阅读
            pre_read_path = os.path.join("pre_reads", f"pre_read_{paper_id}.md")
            if os.path.exists(pre_read_path):
                print(f"使用现有预阅读文件: {pre_read_path}")
                with open(pre_read_path, "r", encoding="utf-8") as f:
                    pre_read_content = f.read()
                return self.create_vector_store_from_pre_read(
                    paper_id, pre_read_content
                )

            # 3. 检查是否有隐藏预阅读
            hidden_pre_read_path = os.path.join(
                "hidden_pre_reads", f"hidden_pre_read_{paper_id}.md"
            )
            if os.path.exists(hidden_pre_read_path):
                print(f"使用现有隐藏预阅读文件: {hidden_pre_read_path}")
                with open(hidden_pre_read_path, "r", encoding="utf-8") as f:
                    hidden_pre_read_content = f.read()
                return self.create_vector_store_from_pre_read(
                    paper_id, hidden_pre_read_content
                )

            # 4. 如果都没有，生成隐藏预阅读
            print(f"论文 {paper_id} 没有预阅读，正在生成隐藏预阅读...")

            # 检查PDF文件
            if not paper_info.get("file_path") or not os.path.exists(
                paper_info["file_path"]
            ):
                print(f"论文 {paper_id} 的PDF文件不存在")
                return False

            # 读取PDF内容
            pdf_content = ""
            try:
                with open(paper_info["file_path"], "rb") as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        pdf_content += page.extract_text()
            except Exception as e:
                print(f"PDF读取错误: {e}")
                return False

            # 读取预阅读提示词
            try:
                with open("static/pre_reads_prompt.md", "r", encoding="utf-8") as f:
                    prompt_content = f.read()
            except Exception as e:
                print(f"提示词文件读取错误: {e}")
                return False

            # 生成隐藏预阅读
            hidden_pre_read = self.generate_hidden_pre_read(
                paper_id, pdf_content, prompt_content
            )
            if hidden_pre_read:
                return self.create_vector_store_from_pre_read(paper_id, hidden_pre_read)

            return False

        except Exception as e:
            print(f"获取或创建RAG上下文失败: {e}")
            return False

    def delete_hidden_pre_read(self, paper_id: int) -> bool:
        """删除隐藏预阅读文件（当用户生成正式预阅读时调用）"""
        try:
            hidden_pre_read_path = os.path.join(
                "hidden_pre_reads", f"hidden_pre_read_{paper_id}.md"
            )
            if os.path.exists(hidden_pre_read_path):
                os.remove(hidden_pre_read_path)
                print(f"已删除隐藏预阅读文件: {hidden_pre_read_path}")

                # 同时删除对应的向量数据库，强制重建
                vector_store_path = self.get_paper_vector_store_path(paper_id)
                if os.path.exists(vector_store_path):
                    import shutil

                    shutil.rmtree(vector_store_path)
                    print(f"已删除隐藏预阅读的向量数据库: {vector_store_path}")

                return True
            return False
        except Exception as e:
            print(f"删除隐藏预阅读失败: {e}")
            return False

    def create_rag_prompt(
        self, query: str, context_docs: List[str], paper_info: Dict
    ) -> str:
        """创建包含RAG上下文的提示词"""
        context_text = "\n\n".join(
            [f"文档片段 {i+1}:\n{doc}" for i, doc in enumerate(context_docs)]
        )

        rag_prompt = f"""你是一个专业的学术论文阅读助手。请基于以下预阅读文档内容来回答用户的问题。

论文信息：
标题：{paper_info.get('title', '未知')}
作者：{paper_info.get('authors', '未知')}
摘要：{paper_info.get('abstract', '无摘要')}

相关文档内容：
{context_text}

请遵循以下准则：
1. 主要基于提供的文档内容回答问题
2. 提供准确、专业的学术分析
3. 用清晰易懂的语言解释复杂概念
4. 如果问题超出文档范围，请明确说明并基于你的知识进行补充
5. 保持客观、中性的学术态度

用户问题：{query}"""

        return rag_prompt

    def search_multi_doc_relevant_documents(
        self, main_paper_id: int, reference_paper_ids: List[int], query: str, k: int = 8
    ) -> List[str]:
        """在多篇论文中搜索相关文档"""
        try:
            all_relevant_docs = []
            paper_doc_map = {}  # 记录文档来源

            # 搜索主论文
            main_docs = self.search_relevant_documents(main_paper_id, query, k=k // 2)
            for doc in main_docs:
                all_relevant_docs.append(doc)
                paper_doc_map[doc] = {"paper_id": main_paper_id, "is_main": True}

            # 搜索参考论文
            docs_per_ref_paper = (
                max(1, k // (2 * len(reference_paper_ids)))
                if reference_paper_ids
                else 0
            )

            for ref_paper_id in reference_paper_ids:
                ref_docs = self.search_relevant_documents(
                    ref_paper_id, query, k=docs_per_ref_paper
                )
                for doc in ref_docs:
                    all_relevant_docs.append(doc)
                    paper_doc_map[doc] = {"paper_id": ref_paper_id, "is_main": False}

            # 为文档添加来源标识
            tagged_docs = []
            for doc in all_relevant_docs[:k]:  # 限制总数量
                paper_info = paper_doc_map.get(doc, {})
                paper_id = paper_info.get("paper_id", "unknown")
                is_main = paper_info.get("is_main", False)

                if is_main:
                    tagged_doc = f"[主论文-{paper_id}] {doc}"
                else:
                    tagged_doc = f"[参考论文-{paper_id}] {doc}"

                tagged_docs.append(tagged_doc)

            print(f"多文档RAG检索到 {len(tagged_docs)} 个相关文档片段")
            return tagged_docs

        except Exception as e:
            print(f"多文档RAG搜索错误: {e}")
            return []

    def create_multi_doc_rag_prompt(
        self,
        query: str,
        relevant_docs: List[str],
        main_paper: Dict,
        reference_papers: List[Dict],
    ) -> str:
        """创建多文档RAG增强的提示词"""

        # 构建文档上下文
        context_text = "\n\n".join(
            [f"文档片段 {i+1}:\n{doc}" for i, doc in enumerate(relevant_docs)]
        )

        # 构建论文信息
        main_paper_info = f"""
当前论文（主要视角）：
标题：{main_paper.get('title', '未知')}
作者：{main_paper.get('authors', '未知')}
来源：{main_paper.get('source', '未知')}
摘要：{main_paper.get('abstract', '无摘要')}"""

        reference_papers_info = ""
        if reference_papers:
            reference_papers_info = "\n\n参考论文："
            for i, ref_paper in enumerate(reference_papers, 1):
                reference_papers_info += f"""
{i}. {ref_paper.get('title', '未知')}
   作者：{ref_paper.get('authors', '未知')}
   来源：{ref_paper.get('source', '未知')}
   摘要：{ref_paper.get('abstract', '无摘要')}"""

        rag_prompt = f"""你是一个专业的学术论文阅读助手，现在正在进行跨文档分析。请站在当前论文的角度，结合参考论文来回答用户的问题。

{main_paper_info}
{reference_papers_info}

相关文档内容：
{context_text}

请遵循以下准则：
1. 以当前论文为主要视角，将其作为核心分析对象
2. 将参考论文作为对比、补充或支持材料，体现梯度对待
3. 在回答中明确指出当前论文与参考论文之间的异同点、关联性或对比
4. 提供准确、专业的学术分析，体现以当前论文为中心的多文档视角
5. 用清晰易懂的语言解释复杂概念
6. 如果问题涉及文档中没有的内容，请明确说明并基于你的知识进行补充
7. 保持客观、中性的学术态度
8. 在引用不同论文内容时，可以参考文档片段中的来源标识，并明确区分当前论文和参考论文的内容

用户问题：{query}"""

        return rag_prompt
