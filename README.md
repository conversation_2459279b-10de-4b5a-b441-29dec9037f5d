# DocuMancer - 论文精灵

一个基于Web的智能论文阅读助手，提供论文管理和AI对话功能，让学术研究更加高效。

## 🌟 功能特性

- 📚 **论文库管理**: 可视化的论文管理界面
- 📄 **PDF上传**: 支持PDF文件上传和自动解析
- 🔗 **arXiv集成**: 通过arXiv ID快速添加论文并自动下载PDF
- 📥 **自动下载**: arXiv论文PDF自动下载到本地存储
- 🤖 **AI智能对话**: 基于OpenRouter和Gemini 2.5模型的论文问答
- 💬 **多轮对话**: 支持上下文感知的连续对话
- 📝 **Markdown渲染**: 完整支持AI回答中的Markdown格式
- 🗑️ **论文删除**: 支持删除论文及其PDF文件
- 📊 **信息提取**: 自动提取论文标题、作者、摘要、日期
- 🏷️ **状态标识**: 清晰显示PDF文件可用性状态
- 📱 **响应式设计**: 完美适配桌面和移动设备
- 🎨 **现代UI**: 暗色主题，动画效果，用户体验优秀

## 🛠️ 技术栈

- **后端**: Python Flask
- **AI模型**: google/gemini-2.5-flash-lite-preview-06-17 (via OpenRouter)
- **前端**: HTML5, CSS3, JavaScript (原生)
- **PDF处理**: PyPDF2
- **外部API**: arXiv API, OpenRouter API
- **文件下载**: Requests库流式下载
- **数据存储**: JSON文件 (轻量级存储)
- **样式**: 现代化暗色主题设计

## 🚀 安装和运行

### 1. 克隆项目

```bash
git clone <repository-url>
cd DocuMancer
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置API密钥

创建 `config.json` 文件：
```json
{
  "llm": {
    "provider": "openrouter",
    "base_url": "https://openrouter.ai/api/v1",
    "api_key": "your-openrouter-api-key",
    "model": "google/gemini-2.5-flash-lite-preview-06-17",
    "max_tokens": 2048,
    "temperature": 0.7
  },
  "app": {
    "debug": true,
    "max_file_size": "16MB"
  }
}
```

### 4. 运行应用

```bash
python app.py
```

### 5. 访问应用

打开浏览器访问: 
- 本地: http://localhost:5000
- 远程: http://**************:5000

## 📁 项目结构

```
DocuMancer/
├── app.py                 # Flask 主应用 (包含LLM集成)
├── config.json           # 配置文件 (包含API密钥)
├── requirements.txt       # Python 依赖
├── .gitignore            # Git忽略文件 (保护敏感信息)
├── templates/
│   ├── index.html        # 论文库主页
│   └── chat.html         # AI聊天页面
├── static/
│   ├── styles.css        # 样式文件 (包含聊天UI)
│   ├── script.js         # 主页JavaScript
│   └── chat.js           # 聊天页面JavaScript
├── uploads/              # PDF文件存储目录
├── data/                 # 数据存储目录
│   └── papers.json       # 论文数据文件
└── README.md             # 项目说明
```

## 💡 使用指南

### 添加论文

1. **上传PDF**: 
   - 点击"上传PDF"按钮，选择本地PDF文件
   - 文件自动保存到`uploads/`目录
   - 自动解析PDF元数据和内容

2. **添加arXiv论文**: 
   - 在输入框中输入arXiv ID (如: 2301.07041)
   - 点击"添加"按钮
   - 系统自动从arXiv获取论文信息
   - **自动下载PDF文件到本地**
   - 显示下载状态和PDF可用性

### AI智能对话

- **进入对话**: 点击论文卡片上的"开始对话"按钮
- **智能回答**: AI助手基于论文内容提供专业回答
- **多轮对话**: 支持连续提问，AI会记住对话上下文
- **建议问题**: 界面提供常见问题模板，一键填入

#### 支持的问题类型
- 论文主要贡献和创新点
- 技术方法和算法解释
- 实验结果和性能分析
- 相关工作和背景介绍
- 论文局限性和未来方向

### AI助手特色

- **专业背景**: 专门针对学术论文阅读优化
- **中文支持**: 完美支持中文问答
- **上下文感知**: 基于论文内容和对话历史回答
- **Markdown渲染**: 支持粗体、斜体、代码等格式
- **错误处理**: 优雅的错误提示和重试机制

## 🎨 界面特色

- **现代设计**: 暗色主题，渐变色彩，圆角设计
- **状态指示**: PDF可用性一目了然
- **直观交互**: 悬停效果，动画过渡，视觉反馈
- **聊天体验**: 实时对话，打字指示器，建议问题
- **响应式**: 自适应不同屏幕尺寸

## 🔧 API端点

- `GET /`: 论文库主页
- `POST /upload_pdf`: PDF文件上传 (保存到uploads目录)
- `POST /add_arxiv`: 添加arXiv论文 (包含PDF下载)
- `GET /chat/<paper_id>`: AI聊天页面
- `POST /chat_api`: AI聊天API (集成OpenRouter)

## 🤖 AI配置说明

### OpenRouter集成
- **Provider**: OpenRouter AI
- **Model**: Google Gemini 2.5 Flash Lite Preview
- **Features**: 多轮对话、上下文理解、中文支持
- **Configuration**: 通过config.json灵活配置

### 系统提示词
AI助手配备专业的学术背景提示词，包括：
- 学术论文分析专长
- 清晰易懂的解释能力
- 客观中性的学术态度
- 基于论文内容的精准回答

## 📋 文件管理

### 存储位置
- **上传的PDF**: `uploads/{timestamp}_{filename}.pdf`
- **arXiv PDF**: `uploads/arxiv_{id}_{timestamp}.pdf`
- **论文数据**: `data/papers.json`
- **配置文件**: `config.json` (包含API密钥)

### 数据结构
```json
{
  "id": 1,
  "title": "论文标题",
  "authors": "作者列表",
  "abstract": "论文摘要",
  "date": "2023-01-17",
  "source": "arXiv:2301.07041",
  "file_path": "uploads/arxiv_2301.07041_20250722_220457.pdf",
  "has_pdf": true,
  "arxiv_id": "2301.07041"
}
```

## 🔒 安全和隐私

- **API密钥保护**: config.json在.gitignore中，不会上传到版本控制
- **本地存储**: 所有论文和对话数据存储在本地
- **错误处理**: 完善的异常处理，保护系统稳定性
- **输入验证**: 严格的用户输入验证和清理

## 📋 后续计划

- [ ] 支持更多LLM模型 (OpenAI GPT, Claude等)
- [ ] 添加论文搜索和过滤功能
- [ ] 实现论文标注和笔记功能
- [ ] 支持更多论文源 (Google Scholar, PubMed等)
- [ ] 用户账户和数据同步
- [ ] 论文相关性分析和推荐
- [ ] 导出功能 (Markdown, PDF摘要等)
- [ ] 批量论文处理
- [ ] 高级搜索和标签系统
- [ ] PDF文件管理和清理功能
- [ ] 对话历史持久化
- [ ] 多语言支持扩展

## 🎯 使用示例

### 典型对话示例
```
🧑 用户: 这篇论文的主要贡献是什么？
🤖 AI: 这篇论文的主要贡献可以归纳为以下几点：

1. 指出了现有全同态加密（FHE）方案中普遍存在的完整性问题及其严重性
2. 揭示了现有FHE完整性解决方案的不足
3. 提出了一个新的安全概念：可验证的全同态加密的恶意安全模型
4. 对新的VFHE概念进行了实例化和性能评估
5. 指出了未来研究方向

总而言之，这篇论文识别并量化了FHE在完整性方面存在的关键安全漏洞...
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## �� 许可证

MIT License 